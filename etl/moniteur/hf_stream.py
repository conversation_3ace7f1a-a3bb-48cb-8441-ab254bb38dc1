#!/usr/bin/env python3
"""
Robust HF streaming ingestion worker for ailex-be-ingest.
Handles rate limits, checkpointing, and batch processing for production use.
"""

import os
import sys
import json
import time
import hashlib
import logging
import argparse
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

import tiktoken
import voyageai
from datasets import load_dataset
from pinecone import Pinecone, ServerlessSpec
from dotenv import load_dotenv
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import requests

# Load environment variables
load_dotenv()

# Ensure logs directory exists
Path('logs').mkdir(exist_ok=True)

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/hf_stream.log', mode='a')
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class ProcessingStats:
    """Track processing statistics"""
    docs_processed: int = 0
    chunks_created: int = 0
    vectors_upserted: int = 0
    total_tokens: int = 0
    start_time: float = 0
    embedding_time: float = 0
    upsert_time: float = 0
    retries: int = 0


class Checkpoint:
    """Handle checkpoint persistence for resumable processing"""
    
    def __init__(self, path: str):
        self.path = Path(path)
        self.path.parent.mkdir(parents=True, exist_ok=True)
    
    def save(self, count: int, last_uri: str) -> None:
        """Save checkpoint state"""
        checkpoint_data = {
            'count': count,
            'last_uri': last_uri,
            'timestamp': datetime.now().isoformat()
        }
        with open(self.path, 'w') as f:
            json.dump(checkpoint_data, f, indent=2)
        logger.info(f"Checkpoint saved: {count} docs, last_uri={last_uri}")
    
    def load(self) -> Tuple[int, Optional[str]]:
        """Load checkpoint state"""
        if not self.path.exists():
            logger.info("No checkpoint found, starting from beginning")
            return 0, None
        
        try:
            with open(self.path, 'r') as f:
                data = json.load(f)
            count = data.get('count', 0)
            last_uri = data.get('last_uri')
            logger.info(f"Checkpoint loaded: {count} docs, last_uri={last_uri}")
            return count, last_uri
        except Exception as e:
            logger.warning(f"Failed to load checkpoint: {e}, starting fresh")
            return 0, None


class RateLimiter:
    """Handle rate limiting with Retry-After and exponential backoff"""
    
    def __init__(self, max_retries: int = 8):
        self.max_retries = max_retries
    
    def handle_rate_limit(self, response_or_exception, attempt: int) -> bool:
        """
        Handle rate limit response. Returns True if should retry.
        """
        if attempt >= self.max_retries:
            logger.error(f"Max retries ({self.max_retries}) exceeded")
            return False
        
        # Handle HTTP response with Retry-After
        if hasattr(response_or_exception, 'headers'):
            retry_after = response_or_exception.headers.get('Retry-After')
            if retry_after:
                sleep_time = int(retry_after)
                logger.warning(f"Rate limited, Retry-After: {sleep_time}s (attempt {attempt})")
                time.sleep(sleep_time)
                return True
        
        # Exponential backoff with jitter
        base_delay = 5  # Start at 5 seconds
        max_delay = 600  # Cap at 10 minutes
        delay = min(base_delay * (2 ** attempt), max_delay)
        jitter = delay * 0.1 * (0.5 - os.urandom(1)[0] / 255)  # ±10% jitter
        sleep_time = delay + jitter
        
        logger.warning(f"Rate limited, backing off {sleep_time:.1f}s (attempt {attempt})")
        time.sleep(sleep_time)
        return True


class HFStreamer:
    """Main streaming ingestion worker"""
    
    def __init__(self, 
                 dataset_name: str,
                 namespace: str = "moniteur",
                 batch_embed: int = 64,
                 checkpoint_path: str = "./data/checkpoints/moniteur.progress"):
        
        self.dataset_name = dataset_name
        self.namespace = namespace
        self.batch_embed = batch_embed
        self.checkpoint = Checkpoint(checkpoint_path)
        self.rate_limiter = RateLimiter()
        self.stats = ProcessingStats()
        
        # Initialize tokenizer
        self.tokenizer = tiktoken.get_encoding("cl100k_base")
        
        # Initialize Voyage AI client
        voyage_key = os.getenv("VOYAGE_API_KEY")
        if not voyage_key:
            raise ValueError("VOYAGE_API_KEY environment variable required")
        self.voyage_client = voyageai.Client(api_key=voyage_key)
        
        # Initialize Pinecone
        self._setup_pinecone()
    
    def _setup_pinecone(self) -> None:
        """Setup Pinecone client and ensure index exists"""
        pinecone_key = os.getenv("PINECONE_API_KEY")
        if not pinecone_key:
            raise ValueError("PINECONE_API_KEY environment variable required")
        
        self.pc = Pinecone(api_key=pinecone_key)

        # Use existing index (prefer PINECONE env var, fallback to PINECONE_INDEX)
        index_name = os.getenv("PINECONE", os.getenv("PINECONE_INDEX", "ailexbe"))

        # Check if index exists
        existing_indexes = [idx.name for idx in self.pc.list_indexes()]
        if index_name not in existing_indexes:
            logger.error(f"Index {index_name} not found!")
            logger.error(f"Available indexes: {[idx.name for idx in existing_indexes]}")
            raise ValueError(f"Index {index_name} does not exist. Please use an existing index or create one manually.")

        self.index = self.pc.Index(index_name)
        logger.info(f"Connected to Pinecone index: {index_name}")
    
    def chunk_text(self, text: str) -> List[str]:
        """Split text into overlapping chunks using tiktoken"""
        if not text or len(text.strip()) < 10:
            return []
        
        tokens = self.tokenizer.encode(text)
        chunk_size = 1000
        overlap = 100
        chunks = []
        
        for i in range(0, len(tokens), chunk_size - overlap):
            chunk_tokens = tokens[i:i + chunk_size]
            if len(chunk_tokens) < 50:  # Skip very small chunks
                continue
            chunk_text = self.tokenizer.decode(chunk_tokens)
            chunks.append(chunk_text)
        
        return chunks
    
    @retry(
        stop=stop_after_attempt(8),
        wait=wait_exponential(multiplier=5, max=600),
        retry=retry_if_exception_type((requests.exceptions.RequestException, Exception))
    )
    def embed_batch(self, texts: List[str]) -> List[List[float]]:
        """Create embeddings for a batch of texts using Voyage AI"""
        if not texts:
            return []
        
        start_time = time.time()
        try:
            result = self.voyage_client.embed(
                texts=texts,
                model="voyage-3-large",
                output_dimension=1024
            )
            
            embedding_time = time.time() - start_time
            self.stats.embedding_time += embedding_time
            
            logger.info(f"Embedded {len(texts)} texts in {embedding_time:.2f}s")
            return result.embeddings
            
        except Exception as e:
            self.stats.retries += 1
            logger.warning(f"Embedding failed, retrying: {e}")
            raise
    
    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=2, max=60),
        retry=retry_if_exception_type(Exception)
    )
    def upsert_batch(self, vectors: List[Dict[str, Any]]) -> None:
        """Upsert a batch of vectors to Pinecone"""
        if not vectors:
            return
        
        start_time = time.time()
        try:
            self.index.upsert(vectors=vectors, namespace=self.namespace)
            
            upsert_time = time.time() - start_time
            self.stats.upsert_time += upsert_time
            self.stats.vectors_upserted += len(vectors)
            
            logger.info(f"Upserted {len(vectors)} vectors in {upsert_time:.2f}s")
            
        except Exception as e:
            self.stats.retries += 1
            logger.warning(f"Upsert failed, retrying: {e}")
            raise

    def process_record(self, record: Dict[str, Any]) -> int:
        """Process a single record and return number of chunks created"""
        uri = record.get('uri', '')
        text = record.get('text', '')
        year = record.get('year')
        month = record.get('month')
        day = record.get('day')
        lang = record.get('lang', 'unknown')

        if not text or len(text.strip()) < 50:
            logger.debug(f"Skipping short text for {uri}")
            return 0

        # Create chunks
        chunks = self.chunk_text(text)
        if not chunks:
            logger.debug(f"No chunks created for {uri}")
            return 0

        self.stats.chunks_created += len(chunks)
        self.stats.total_tokens += len(self.tokenizer.encode(text))

        # Process chunks in batches
        vectors_to_upsert = []

        for i in range(0, len(chunks), self.batch_embed):
            batch_chunks = chunks[i:i + self.batch_embed]

            # Create embeddings
            embeddings = self.embed_batch(batch_chunks)

            # Prepare vectors
            for chunk_idx, (chunk_text, embedding) in enumerate(zip(batch_chunks, embeddings)):
                # Create unique vector ID
                vector_id = hashlib.md5(f"{uri}::{i + chunk_idx}::{lang}".encode()).hexdigest()

                # Create metadata with null handling
                metadata = {
                    "eli": uri or "unknown",
                    "lang": lang or "unknown",
                    "year": year if year is not None else 0,
                    "month": month if month is not None else 0,
                    "day": day if day is not None else 0,
                    "source": "Moniteur",
                    "preview": chunk_text[:400]
                }

                vectors_to_upsert.append({
                    "id": vector_id,
                    "values": embedding,
                    "metadata": metadata
                })

        # Upsert all vectors for this record
        if vectors_to_upsert:
            self.upsert_batch(vectors_to_upsert)

        return len(chunks)

    def stream_dataset(self, limit: Optional[int] = None, resume: bool = True) -> None:
        """Main streaming processing loop"""
        self.stats.start_time = time.time()

        # Load checkpoint
        checkpoint_count, last_uri = self.checkpoint.load() if resume else (0, None)

        # Setup HF authentication
        hf_token = os.getenv("HF_TOKEN") or os.getenv("HUGGING_FACE_HUB_TOKEN")

        logger.info(f"Starting stream processing of {self.dataset_name}")
        logger.info(f"Resume: {resume}, Last URI: {last_uri}, Limit: {limit}")

        try:
            # Set longer timeout for large datasets
            import socket
            original_timeout = socket.getdefaulttimeout()
            socket.setdefaulttimeout(300)  # 5 minutes

            # Load dataset with authentication
            load_kwargs = {
                "split": "train",
                "streaming": True
            }
            if hf_token and hf_token != "your_huggingface_token_here":
                load_kwargs["token"] = hf_token  # Updated parameter name

            logger.info(f"Loading dataset {self.dataset_name} with extended timeout (300s)...")
            ds = load_dataset(self.dataset_name, **load_kwargs)

            # Restore original timeout
            socket.setdefaulttimeout(original_timeout)

            # Skip to checkpoint if resuming
            skipping = last_uri is not None
            processed_count = checkpoint_count

            for record in ds:
                try:
                    uri = record.get('uri', '')

                    # Skip until we reach the checkpoint
                    if skipping:
                        if uri == last_uri:
                            skipping = False
                            logger.info(f"Resumed from checkpoint at {uri}")
                        continue

                    # Process the record
                    chunks_created = self.process_record(record)
                    processed_count += 1
                    self.stats.docs_processed += 1

                    # Log progress
                    if processed_count % 100 == 0:
                        elapsed = time.time() - self.stats.start_time
                        rate = processed_count / elapsed if elapsed > 0 else 0
                        logger.info(f"Progress: {processed_count} docs, {self.stats.chunks_created} chunks, {rate:.1f} docs/sec")

                    # Save checkpoint periodically
                    if processed_count % 50 == 0:
                        self.checkpoint.save(processed_count, uri)

                    # Check limit
                    if limit and processed_count >= limit:
                        logger.info(f"Reached limit of {limit} documents")
                        break

                except Exception as e:
                    logger.error(f"Error processing record {uri}: {e}")
                    self.stats.retries += 1
                    continue

            # Final checkpoint
            if processed_count > checkpoint_count:
                self.checkpoint.save(processed_count, uri)

        except Exception as e:
            logger.error(f"Fatal error in stream processing: {e}")
            raise

        # Print final statistics
        self._print_final_stats()

    def _print_final_stats(self) -> None:
        """Print comprehensive final statistics"""
        elapsed = time.time() - self.stats.start_time

        stats_summary = {
            "event": "processing_complete",
            "docs_processed": self.stats.docs_processed,
            "chunks_created": self.stats.chunks_created,
            "vectors_upserted": self.stats.vectors_upserted,
            "total_tokens": self.stats.total_tokens,
            "elapsed_seconds": elapsed,
            "docs_per_second": self.stats.docs_processed / elapsed if elapsed > 0 else 0,
            "embedding_time": self.stats.embedding_time,
            "upsert_time": self.stats.upsert_time,
            "retries": self.stats.retries
        }

        logger.info(f"Final stats: {json.dumps(stats_summary, indent=2)}")

        print("\n" + "="*60)
        print("🎉 PROCESSING COMPLETE")
        print("="*60)
        print(f"📄 Documents processed: {self.stats.docs_processed:,}")
        print(f"📝 Chunks created: {self.stats.chunks_created:,}")
        print(f"🔮 Vectors upserted: {self.stats.vectors_upserted:,}")
        print(f"🔤 Total tokens: {self.stats.total_tokens:,}")
        print(f"⏱️  Total time: {elapsed:.1f}s")
        print(f"📈 Processing rate: {self.stats.docs_processed / elapsed:.1f} docs/sec")
        print(f"🔄 Retries: {self.stats.retries}")
        print("="*60)


def main():
    """CLI entry point"""
    parser = argparse.ArgumentParser(
        description="HF streaming ingestion worker for ailex-be-ingest",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Test run with 200 documents
  python -m etl.moniteur.hf_stream --dataset guust-franssens/belgian-journal --limit 200

  # Full production run with resume
  python -m etl.moniteur.hf_stream --dataset guust-franssens/belgian-journal --resume

  # Custom batch size and namespace
  python -m etl.moniteur.hf_stream --dataset guust-franssens/belgisch-staatsblad --namespace test --batch-embed 32

Environment variables required:
  HF_TOKEN or HUGGING_FACE_HUB_TOKEN - Hugging Face authentication
  VOYAGE_API_KEY - Voyage AI API key
  PINECONE_API_KEY - Pinecone API key
  PINECONE_ENV - Pinecone environment (default: us-east-1)
  PINECONE_INDEX - Pinecone index name (default: ailex-be-legislation)
        """
    )

    parser.add_argument(
        "--dataset",
        default="guust-franssens/belgian-journal",
        help="HuggingFace dataset name (default: guust-franssens/belgian-journal)"
    )

    parser.add_argument(
        "--namespace",
        default="moniteur",
        help="Pinecone namespace (default: moniteur)"
    )

    parser.add_argument(
        "--resume",
        action="store_true",
        help="Resume from checkpoint"
    )

    parser.add_argument(
        "--no-resume",
        dest="resume",
        action="store_false",
        help="Start from beginning (ignore checkpoint)"
    )

    parser.add_argument(
        "--limit",
        type=int,
        help="Limit number of documents to process (for testing)"
    )

    parser.add_argument(
        "--batch-embed",
        type=int,
        default=int(os.getenv("BATCH_EMBED", "64")),
        help="Batch size for embedding (default: 64)"
    )

    parser.add_argument(
        "--checkpoint-path",
        default=os.getenv("CHECKPOINT_PATH", "./data/checkpoints/moniteur.progress"),
        help="Path to checkpoint file"
    )

    parser.set_defaults(resume=True)
    args = parser.parse_args()

    # Validate environment variables
    required_env_vars = ["VOYAGE_API_KEY", "PINECONE_API_KEY"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        logger.error(f"Missing required environment variables: {missing_vars}")
        sys.exit(1)

    # Check HF authentication
    hf_token = os.getenv("HF_TOKEN") or os.getenv("HUGGING_FACE_HUB_TOKEN")
    if not hf_token:
        logger.warning("No HF_TOKEN found - may hit rate limits without authentication")

    # Create streamer and run
    try:
        streamer = HFStreamer(
            dataset_name=args.dataset,
            namespace=args.namespace,
            batch_embed=args.batch_embed,
            checkpoint_path=args.checkpoint_path
        )

        logger.info(f"Starting HF stream processing")
        logger.info(f"Dataset: {args.dataset}")
        logger.info(f"Namespace: {args.namespace}")
        logger.info(f"Batch size: {args.batch_embed}")
        logger.info(f"Resume: {args.resume}")
        logger.info(f"Limit: {args.limit}")

        streamer.stream_dataset(limit=args.limit, resume=args.resume)

    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
