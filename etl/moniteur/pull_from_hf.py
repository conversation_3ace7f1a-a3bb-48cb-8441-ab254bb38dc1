#!/usr/bin/env python3
"""
Stream Moniteur Belge from Hugging Face dataset and push to Pinecone.
Uses the guust-franssens/belgisch-staatsblad dataset (CC-0 license)
which contains 30 years of Belgian State Gazette content (1995-2025).
"""

import os
import hashlib
import itertools
import time
from pathlib import Path
from dotenv import load_dotenv
from datasets import load_dataset
import tiktoken
import voyageai
from pinecone import Pinecone

# Load environment variables
load_dotenv()

# Configuration
CHUNK_SIZE = 1000
OVERLAP = 100
BATCH_SIZE = 10  # Process embeddings in batches for efficiency

def setup_pinecone():
    """Initialize Pinecone client and index"""
    pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))

    # Use the existing ailexbe index (from .env PINECONE variable)
    index_name = os.getenv("PINECONE", "ailexbe")

    # Check if index exists
    existing_indexes = [idx.name for idx in pc.list_indexes()]
    if index_name not in existing_indexes:
        print(f"❌ Index {index_name} not found!")
        print(f"Available indexes: {[idx.name for idx in existing_indexes]}")
        raise ValueError(f"Index {index_name} does not exist")
    else:
        print(f"✅ Using existing index: {index_name}")

    return pc.Index(index_name)

def chunk_text(text: str):
    """Split text into overlapping chunks using tiktoken"""
    enc = tiktoken.get_encoding("cl100k_base")
    tokens = enc.encode(text)
    
    for i in range(0, len(tokens), CHUNK_SIZE - OVERLAP):
        chunk_tokens = tokens[i:i + CHUNK_SIZE]
        yield enc.decode(chunk_tokens)

def embed_batch(texts: list) -> list:
    """Create embeddings for a batch of texts using Voyage AI"""
    if not os.getenv("VOYAGE_API_KEY"):
        raise ValueError("❌ VOYAGE_API_KEY environment variable is not set")
    
    client = voyageai.Client(api_key=os.getenv("VOYAGE_API_KEY"))
    result = client.embed(
        texts=texts,
        model="voyage-3-large"
    )
    return result.embeddings

def process_record(record, index, stats):
    """Process a single record from the HF dataset"""
    # Extract metadata
    meta = {
        "year": record.get("year"),
        "month": record.get("month"), 
        "day": record.get("day"),
        "lang": record.get("lang", "unknown"),
        "source": "Moniteur Belge",
        "eli": record.get("uri", "")
    }
    
    # Get text content
    text = record.get("text", "")
    if not text or len(text.strip()) < 50:  # Skip very short texts
        stats["skipped"] += 1
        return
    
    # Create chunks
    chunks = list(chunk_text(text))
    if not chunks:
        stats["skipped"] += 1
        return
    
    print(f"📄 Processing {meta['year']}-{meta['month']:02d}-{meta['day']:02d} ({meta['lang']}) - {len(chunks)} chunks")
    
    # Process chunks in batches
    vectors_to_upsert = []
    
    for i in range(0, len(chunks), BATCH_SIZE):
        batch_chunks = chunks[i:i + BATCH_SIZE]
        
        try:
            # Create embeddings for batch
            embeddings = embed_batch(batch_chunks)
            
            # Create vectors
            for chunk_content, embedding in zip(batch_chunks, embeddings):
                vector_id = hashlib.md5(chunk_content.encode()).hexdigest()
                vectors_to_upsert.append({
                    "id": vector_id,
                    "values": embedding,
                    "metadata": {
                        **meta,
                        "text": chunk_content[:500]  # Store preview of text
                    }
                })
            
            stats["chunks_processed"] += len(batch_chunks)
            
        except Exception as e:
            print(f"❌ Error processing batch: {e}")
            stats["errors"] += 1
            continue
    
    # Upsert to Pinecone
    if vectors_to_upsert:
        try:
            index.upsert(vectors=vectors_to_upsert, namespace="moniteur")
            stats["vectors_upserted"] += len(vectors_to_upsert)
            print(f"✅ Uploaded {len(vectors_to_upsert)} vectors to Pinecone")
        except Exception as e:
            print(f"❌ Error upserting to Pinecone: {e}")
            stats["errors"] += 1
    
    stats["records_processed"] += 1

def main(max_records=None):
    """Main ETL function"""
    print("🚀 Starting Moniteur Belge ETL from Hugging Face dataset")
    print("📊 Dataset: guust-franssens/belgisch-staatsblad (CC-0)")

    if max_records:
        print(f"🧪 Running in test mode - processing max {max_records} records")

    # Setup
    index = setup_pinecone()

    # Initialize stats
    stats = {
        "records_processed": 0,
        "chunks_processed": 0,
        "vectors_upserted": 0,
        "skipped": 0,
        "errors": 0
    }

    start_time = time.time()

    try:
        # Load dataset in streaming mode
        print("📥 Loading dataset from Hugging Face...")
        try:
            ds = load_dataset(
                "guust-franssens/belgisch-staatsblad",
                split="train",
                streaming=True
            )
        except Exception as e:
            print(f"❌ Error loading dataset: {e}")
            print("💡 Trying alternative dataset name...")
            # Try the alternative dataset name mentioned in the description
            ds = load_dataset(
                "guust-franssens/belgian-journal",
                split="train",
                streaming=True
            )

        print("🔄 Processing records...")

        # Process each record
        for i, record in enumerate(ds):
            try:
                process_record(record, index, stats)

                # Print progress every 10 records in test mode, 100 in normal mode
                progress_interval = 10 if max_records else 100
                if (i + 1) % progress_interval == 0:
                    elapsed = time.time() - start_time
                    print(f"📈 Progress: {i + 1} records processed in {elapsed:.1f}s")
                    print(f"   Stats: {stats['chunks_processed']} chunks, {stats['vectors_upserted']} vectors, {stats['errors']} errors")

                # Stop if we've reached max_records
                if max_records and (i + 1) >= max_records:
                    print(f"🛑 Reached maximum records limit ({max_records})")
                    break

                # Small delay to avoid rate limiting
                if (i + 1) % 50 == 0:
                    time.sleep(1)

            except KeyboardInterrupt:
                print("\n⏹️ Interrupted by user")
                break
            except Exception as e:
                print(f"❌ Error processing record {i}: {str(e)}")
                stats["errors"] += 1
                continue

    except Exception as e:
        print(f"❌ Fatal error: {e}")
        return False
    
    # Final stats
    elapsed = time.time() - start_time
    print(f"\n✅ ETL completed in {elapsed:.1f}s")
    print(f"📊 Final stats:")
    print(f"   Records processed: {stats['records_processed']}")
    print(f"   Chunks created: {stats['chunks_processed']}")
    print(f"   Vectors upserted: {stats['vectors_upserted']}")
    print(f"   Records skipped: {stats['skipped']}")
    print(f"   Errors: {stats['errors']}")
    
    return True

if __name__ == "__main__":
    import sys

    # Check for test mode
    max_records = None
    if len(sys.argv) > 1:
        if sys.argv[1].lower() == "test":
            max_records = 5  # Process only 5 records in test mode
        else:
            try:
                max_records = int(sys.argv[1])
            except ValueError:
                print("Usage: python pull_from_hf.py [test|number_of_records]")
                exit(1)

    success = main(max_records)
    exit(0 if success else 1)
