#!/usr/bin/env python3
"""
Comprehensive Neo4j test suite for ailex-be-ingest.
Tests all Neo4j functionality including the custom Neo4jClient.
"""

import sys
import time
from pathlib import Path
from datetime import date

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from common.config import config, setup_logging
from common.neo4j import Neo4jClient
from common.models import CommonAct, CommonArticle, LegalRelationship

def test_basic_connection():
    """Test basic Neo4j connection."""
    print("🔍 Testing basic connection...")
    
    try:
        with Neo4jClient() as client:
            with client.driver.session() as session:
                result = session.run("RETURN 'Hello Neo4j!' as message")
                message = result.single()["message"]
                print(f"✅ Basic connection successful: {message}")
                return True
    except Exception as e:
        print(f"❌ Basic connection failed: {e}")
        return False

def test_schema_operations():
    """Test schema-related operations."""
    print("🔍 Testing schema operations...")
    
    try:
        with Neo4jClient() as client:
            with client.driver.session() as session:
                # Check existing constraints
                result = session.run("SHOW CONSTRAINTS")
                constraints = list(result)
                print(f"✅ Found {len(constraints)} constraints")
                
                # Check existing indexes
                result = session.run("SHOW INDEXES")
                indexes = list(result)
                print(f"✅ Found {len(indexes)} indexes")
                
                return True
    except Exception as e:
        print(f"❌ Schema operations failed: {e}")
        return False

def test_act_operations():
    """Test legal act CRUD operations."""
    print("🔍 Testing legal act operations...")
    
    try:
        # Create test act
        test_act = CommonAct(
            id="test_act_001",
            title="Test Legal Act",
            date=date.today(),
            language="en",
            source="vlaamse",
            eli="http://test.eli/act/001",
            metadata={"test": True, "created_by": "test_suite"}
        )
        
        with Neo4jClient() as client:
            # Test upsert
            success = client.upsert_act(test_act)
            if success:
                print("✅ Act upsert successful")
            else:
                print("❌ Act upsert failed")
                return False
            
            # Test retrieval
            with client.driver.session() as session:
                result = session.run(
                    "MATCH (a:Act {id: $id}) RETURN a.title as title, a.metadata as metadata",
                    {"id": test_act.id}
                )
                record = result.single()
                if record and record["title"] == test_act.title:
                    print("✅ Act retrieval successful")
                    print(f"   Title: {record['title']}")
                    print(f"   Metadata: {record['metadata']}")
                else:
                    print("❌ Act retrieval failed")
                    return False
            
            # Clean up
            with client.driver.session() as session:
                session.run("MATCH (a:Act {id: $id}) DELETE a", {"id": test_act.id})
                print("✅ Test act cleaned up")
            
            return True
            
    except Exception as e:
        print(f"❌ Act operations failed: {e}")
        return False

def test_article_operations():
    """Test article CRUD operations."""
    print("🔍 Testing article operations...")
    
    try:
        # Create test act first
        test_act = CommonAct(
            id="test_act_002",
            title="Test Act for Articles",
            date=date.today(),
            language="en",
            source="vlaamse",
            metadata={"test": True}
        )
        
        test_article = CommonArticle(
            id="test_act_002_art_1",
            act_id="test_act_002",
            number="1",
            heading="Test Article",
            text="This is a test article for the test suite.",
            language="en",
            metadata={"test": True, "created_by": "test_suite"}
        )
        
        with Neo4jClient() as client:
            # Create act first
            client.upsert_act(test_act)
            
            # Test article upsert
            success = client.upsert_article(test_article)
            if success:
                print("✅ Article upsert successful")
            else:
                print("❌ Article upsert failed")
                return False
            
            # Test relationship creation
            with client.driver.session() as session:
                result = session.run("""
                    MATCH (act:Act {id: $act_id})-[r:HAS_ARTICLE]->(art:Article {id: $art_id})
                    RETURN act.title as act_title, art.heading as art_heading
                """, {"act_id": test_act.id, "art_id": test_article.id})
                
                record = result.single()
                if record:
                    print("✅ Act-Article relationship verified")
                    print(f"   Act: {record['act_title']}")
                    print(f"   Article: {record['art_heading']}")
                else:
                    print("❌ Act-Article relationship not found")
                    return False
            
            # Clean up
            with client.driver.session() as session:
                session.run("""
                    MATCH (act:Act {id: $act_id})
                    OPTIONAL MATCH (act)-[:HAS_ARTICLE]->(art:Article)
                    DELETE act, art
                """, {"act_id": test_act.id})
                print("✅ Test data cleaned up")
            
            return True
            
    except Exception as e:
        print(f"❌ Article operations failed: {e}")
        return False

def test_database_stats():
    """Test database statistics queries."""
    print("🔍 Testing database statistics...")
    
    try:
        with Neo4jClient() as client:
            with client.driver.session() as session:
                # Count nodes by type
                result = session.run("MATCH (n:Act) RETURN count(n) as act_count")
                act_count = result.single()["act_count"]
                print(f"✅ Acts in database: {act_count}")
                
                result = session.run("MATCH (n:Article) RETURN count(n) as article_count")
                article_count = result.single()["article_count"]
                print(f"✅ Articles in database: {article_count}")
                
                # Count relationships
                result = session.run("MATCH ()-[r:HAS_ARTICLE]->() RETURN count(r) as rel_count")
                rel_count = result.single()["rel_count"]
                print(f"✅ HAS_ARTICLE relationships: {rel_count}")
                
                return True
                
    except Exception as e:
        print(f"❌ Database stats failed: {e}")
        return False

def main():
    """Main test function."""
    setup_logging()
    
    print("🚀 Comprehensive Neo4j Test Suite")
    print("=" * 50)
    
    tests = [
        ("Basic Connection", test_basic_connection),
        ("Schema Operations", test_schema_operations),
        ("Act Operations", test_act_operations),
        ("Article Operations", test_article_operations),
        ("Database Statistics", test_database_stats),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results[test_name] = False
        print()
    
    # Summary
    print("📊 Test Results Summary")
    print("=" * 30)
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! Neo4j is ready for production use.")
    else:
        print(f"\n⚠️  {total-passed} test(s) failed. Please review the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
