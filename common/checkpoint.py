"""
Checkpoint management for ailex-be-ingest.

Provides persistent cursor storage to enable resumable ingestion after
interruptions. Supports both local file and Google Cloud Storage backends.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Union
from abc import ABC, abstractmethod

from google.cloud import storage
from google.cloud.exceptions import NotFound

from .config import config

logger = logging.getLogger(__name__)


class CheckpointBackend(ABC):
    """Abstract base class for checkpoint storage backends."""
    
    @abstractmethod
    def read(self) -> Dict[str, Any]:
        """Read checkpoint data."""
        pass
    
    @abstractmethod
    def write(self, data: Dict[str, Any]) -> None:
        """Write checkpoint data."""
        pass
    
    @abstractmethod
    def delete(self) -> None:
        """Delete checkpoint data."""
        pass
    
    @abstractmethod
    def exists(self) -> bool:
        """Check if checkpoint exists."""
        pass


class LocalFileBackend(CheckpointBackend):
    """Local file system checkpoint backend."""
    
    def __init__(self, file_path: Union[str, Path]):
        self.file_path = Path(file_path)
        self.file_path.parent.mkdir(parents=True, exist_ok=True)
    
    def read(self) -> Dict[str, Any]:
        """Read checkpoint from local file."""
        if not self.file_path.exists():
            return {}
        
        try:
            with open(self.file_path, 'r') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            logger.warning(f"Failed to read checkpoint from {self.file_path}: {e}")
            return {}
    
    def write(self, data: Dict[str, Any]) -> None:
        """Write checkpoint to local file."""
        try:
            with open(self.file_path, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            logger.debug(f"Checkpoint written to {self.file_path}")
        except IOError as e:
            logger.error(f"Failed to write checkpoint to {self.file_path}: {e}")
            raise
    
    def delete(self) -> None:
        """Delete checkpoint file."""
        if self.file_path.exists():
            self.file_path.unlink()
            logger.info(f"Checkpoint deleted: {self.file_path}")
    
    def exists(self) -> bool:
        """Check if checkpoint file exists."""
        return self.file_path.exists()


class GCSBackend(CheckpointBackend):
    """Google Cloud Storage checkpoint backend."""
    
    def __init__(self, bucket_name: str, blob_path: str):
        self.bucket_name = bucket_name
        self.blob_path = blob_path
        
        try:
            self.client = storage.Client()
            self.bucket = self.client.bucket(bucket_name)
        except Exception as e:
            logger.warning(f"Failed to initialize GCS client: {e}")
            raise
    
    def read(self) -> Dict[str, Any]:
        """Read checkpoint from GCS."""
        try:
            blob = self.bucket.blob(self.blob_path)
            if not blob.exists():
                return {}
            
            content = blob.download_as_text()
            return json.loads(content)
        except (NotFound, json.JSONDecodeError) as e:
            logger.warning(f"Failed to read checkpoint from gs://{self.bucket_name}/{self.blob_path}: {e}")
            return {}
        except Exception as e:
            logger.error(f"GCS checkpoint read error: {e}")
            raise
    
    def write(self, data: Dict[str, Any]) -> None:
        """Write checkpoint to GCS."""
        try:
            blob = self.bucket.blob(self.blob_path)
            content = json.dumps(data, indent=2, default=str)
            blob.upload_from_string(content, content_type='application/json')
            logger.debug(f"Checkpoint written to gs://{self.bucket_name}/{self.blob_path}")
        except Exception as e:
            logger.error(f"Failed to write checkpoint to GCS: {e}")
            raise
    
    def delete(self) -> None:
        """Delete checkpoint from GCS."""
        try:
            blob = self.bucket.blob(self.blob_path)
            if blob.exists():
                blob.delete()
                logger.info(f"Checkpoint deleted: gs://{self.bucket_name}/{self.blob_path}")
        except Exception as e:
            logger.error(f"Failed to delete checkpoint from GCS: {e}")
            raise
    
    def exists(self) -> bool:
        """Check if checkpoint exists in GCS."""
        try:
            blob = self.bucket.blob(self.blob_path)
            return blob.exists()
        except Exception as e:
            logger.error(f"Failed to check checkpoint existence in GCS: {e}")
            return False


class Checkpoint:
    """
    Checkpoint manager for persistent cursor storage.
    
    Automatically chooses between local file and GCS backend based on
    configuration and availability.
    """
    
    def __init__(self, source: str, use_gcs: Optional[bool] = None):
        """
        Initialize checkpoint manager.
        
        Args:
            source: Source identifier (e.g., 'vlaamse', 'eurlex', 'cellar')
            use_gcs: Force GCS usage. If None, auto-detect based on config
        """
        self.source = source
        self.last_data: Optional[Dict[str, Any]] = None
        
        # Determine backend
        if use_gcs is None:
            use_gcs = bool(config.GCS_BUCKET and config.GCS_CREDENTIALS_PATH)
        
        if use_gcs and config.GCS_BUCKET:
            try:
                blob_path = f"checkpoints/{source}.json"
                self.backend = GCSBackend(config.GCS_BUCKET, blob_path)
                logger.info(f"Using GCS checkpoint backend for {source}")
            except Exception as e:
                logger.warning(f"GCS backend failed, falling back to local: {e}")
                self.backend = self._create_local_backend()
        else:
            self.backend = self._create_local_backend()
    
    def _create_local_backend(self) -> LocalFileBackend:
        """Create local file backend."""
        checkpoint_path = config.CHECKPOINTS_DIR / f"{self.source}.json"
        logger.info(f"Using local checkpoint backend: {checkpoint_path}")
        return LocalFileBackend(checkpoint_path)
    
    def read(self) -> Dict[str, Any]:
        """
        Read current checkpoint data.
        
        Returns:
            Dictionary with checkpoint data, empty if no checkpoint exists
        """
        try:
            data = self.backend.read()
            self.last_data = data.copy() if data else {}
            
            if data:
                logger.info(f"Loaded checkpoint for {self.source}: {data.get('cursor', 'no cursor')}")
            else:
                logger.info(f"No checkpoint found for {self.source}, starting fresh")
            
            return data
        except Exception as e:
            logger.error(f"Failed to read checkpoint for {self.source}: {e}")
            return {}
    
    def write(self, data: Dict[str, Any]) -> None:
        """
        Write checkpoint data.
        
        Args:
            data: Checkpoint data to persist
        """
        # Add metadata
        checkpoint_data = {
            **data,
            'source': self.source,
            'updated_at': datetime.now().isoformat(),
            'version': '1.1'
        }
        
        try:
            self.backend.write(checkpoint_data)
            self.last_data = checkpoint_data.copy()
            logger.debug(f"Checkpoint updated for {self.source}")
        except Exception as e:
            logger.error(f"Failed to write checkpoint for {self.source}: {e}")
            raise
    
    def delete(self) -> None:
        """Delete checkpoint data (reset)."""
        try:
            self.backend.delete()
            self.last_data = None
            logger.info(f"Checkpoint reset for {self.source}")
        except Exception as e:
            logger.error(f"Failed to delete checkpoint for {self.source}: {e}")
            raise
    
    def exists(self) -> bool:
        """Check if checkpoint exists."""
        return self.backend.exists()
    
    def get_cursor(self, default: Any = None) -> Any:
        """Get cursor value from checkpoint."""
        data = self.read()
        return data.get('cursor', default)
    
    def update_cursor(self, cursor: Any, **additional_data) -> None:
        """Update cursor and optionally additional data."""
        data = {'cursor': cursor, **additional_data}
        self.write(data)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get checkpoint statistics."""
        data = self.read()
        return {
            'source': self.source,
            'exists': bool(data),
            'cursor': data.get('cursor'),
            'updated_at': data.get('updated_at'),
            'version': data.get('version'),
            'backend_type': type(self.backend).__name__
        }


# Convenience functions for common checkpoint patterns
def create_vlaamse_checkpoint() -> Checkpoint:
    """Create checkpoint for Vlaamse Codex source."""
    return Checkpoint('vlaamse')


def create_eurlex_checkpoint() -> Checkpoint:
    """Create checkpoint for EUR-Lex source."""
    return Checkpoint('eurlex')


def create_cellar_checkpoint() -> Checkpoint:
    """Create checkpoint for CELLAR source."""
    return Checkpoint('cellar')
