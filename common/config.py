"""
Configuration management for ailex-be-ingest.

Centralizes all environment variable handling and provides
default values for the application.
"""

import os
from pathlib import Path
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Config:
    """Configuration class for ailex-be-ingest."""
    
    # Core directories
    PROJECT_ROOT = Path(__file__).parent.parent
    DATA_DIR = PROJECT_ROOT / "data"
    RAW_DATA_DIR = DATA_DIR / "raw"
    PROCESSED_DATA_DIR = DATA_DIR / "processed"
    CHECKPOINTS_DIR = DATA_DIR / "checkpoints"
    LOGS_DIR = PROJECT_ROOT / "logs"
    
    # Ensure directories exist
    for dir_path in [DATA_DIR, RAW_DATA_DIR, PROCESSED_DATA_DIR, CHECKPOINTS_DIR, LOGS_DIR]:
        dir_path.mkdir(parents=True, exist_ok=True)
    
    # Voyage AI Configuration
    VOYAGE_API_KEY: str = os.getenv("VOYAGE_API_KEY", "")
    VOYAGE_MODEL: str = os.getenv("VOYAGE_MODEL", "voyage-3-large")
    VOYAGE_DIMENSION: int = int(os.getenv("VOYAGE_DIMENSION", "1024"))
    
    # Pinecone Configuration
    PINECONE_API_KEY: str = os.getenv("PINECONE_API_KEY", "")
    PINECONE_ENVIRONMENT: str = os.getenv("PINECONE_ENV", "us-east-1")
    PINECONE_INDEX: str = os.getenv("PINECONE_INDEX", os.getenv("PINECONE", "ailexbe"))
    
    # Neo4j Configuration
    NEO4J_URI: str = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    NEO4J_USERNAME: str = os.getenv("NEO4J_USERNAME", "neo4j")
    NEO4J_PASSWORD: str = os.getenv("NEO4J_PASSWORD", "password")

    # Supabase Configuration (for Global Registry)
    # Environment-aware Supabase configuration
    SUPABASE_URL: str = ""
    SUPABASE_KEY: str = ""

    @classmethod
    def get_current_branch(cls) -> str:
        """Get current git branch."""
        try:
            import subprocess
            result = subprocess.run(
                ["git", "rev-parse", "--abbrev-ref", "HEAD"],
                capture_output=True,
                text=True,
                cwd=cls.PROJECT_ROOT
            )
            if result.returncode == 0:
                return result.stdout.strip()
        except Exception:
            pass
        return "main"  # Default fallback

    @classmethod
    def get_supabase_config(cls) -> tuple[str, str]:
        """Get Supabase URL and key based on current branch."""
        current_branch = cls.get_current_branch()

        if current_branch == "develop":
            url = os.getenv("SUPABASE_URL_DEVELOP", "")
            key = os.getenv("SUPABASE_KEY_DEVELOP", "")
            if url and key:
                return url, key

        # Use main/production for all other branches or as fallback
        url = os.getenv("SUPABASE_URL", "")
        key = os.getenv("SUPABASE_KEY", "")
        return url, key
    
    # Google Cloud Storage Configuration
    GCS_BUCKET: str = os.getenv("GCS_BUCKET", "ailex-be")
    GCS_CREDENTIALS_PATH: Optional[str] = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
    
    # Hugging Face Configuration
    HF_TOKEN: str = os.getenv("HF_TOKEN", os.getenv("HUGGING_FACE_HUB_TOKEN", ""))
    
    # Processing Configuration
    CHUNK_SIZE: int = int(os.getenv("CHUNK_SIZE", "1000"))
    CHUNK_OVERLAP: int = int(os.getenv("CHUNK_OVERLAP", "100"))
    BATCH_EMBED_SIZE: int = int(os.getenv("BATCH_EMBED", "64"))
    
    # Vlaamse Codex Configuration
    VLAAMSE_CODEX_BASE_URL: str = os.getenv(
        "VLAAMSE_CODEX_BASE_URL", 
        "https://codex.vlaanderen.be/Wetgeving/v1"
    )
    VLAAMSE_CODEX_RATE_LIMIT: float = float(os.getenv("VLAAMSE_CODEX_RATE_LIMIT", "1.0"))  # requests per second
    
    # EUR-Lex Configuration
    EURLEX_BULK_URL: str = os.getenv(
        "EURLEX_BULK_URL",
        "https://publications.europa.eu/webapi/rdf/sparql"
    )
    EURLEX_LOGIN_URL: str = os.getenv(
        "EURLEX_LOGIN_URL",
        "https://webgate.ec.europa.eu/cas/login"
    )
    EURLEX_USERNAME: str = os.getenv("EURLEX_USERNAME", "")
    EURLEX_PASSWORD: str = os.getenv("EURLEX_PASSWORD", "")
    
    # CELLAR Configuration
    CELLAR_SPARQL_ENDPOINT: str = os.getenv(
        "CELLAR_SPARQL_ENDPOINT",
        "http://publications.europa.eu/webapi/rdf/sparql"
    )
    CELLAR_REST_BASE_URL: str = os.getenv(
        "CELLAR_REST_BASE_URL",
        "http://publications.europa.eu/resource/celex"
    )
    
    # Checkpoint Configuration
    MONITEUR_CHECKPOINT: str = str(CHECKPOINTS_DIR / "moniteur.progress")
    VLAAMSE_CHECKPOINT: str = str(CHECKPOINTS_DIR / "vlaamse.progress")
    EURLEX_CHECKPOINT: str = str(CHECKPOINTS_DIR / "eurlex.progress")
    CELLAR_CHECKPOINT: str = str(CHECKPOINTS_DIR / "cellar.progress")
    
    # Logging Configuration
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Testing Configuration
    TEST_MODE: bool = os.getenv("TEST_MODE", "false").lower() == "true"
    TEST_LIMIT: int = int(os.getenv("TEST_LIMIT", "10"))
    
    @classmethod
    def validate(cls) -> list[str]:
        """
        Validate required configuration values.
        
        Returns:
            List of missing required environment variables
        """
        required_vars = []
        
        if not cls.VOYAGE_API_KEY:
            required_vars.append("VOYAGE_API_KEY")
        
        if not cls.PINECONE_API_KEY:
            required_vars.append("PINECONE_API_KEY")
        
        # Neo4j is optional for basic operation but required for full functionality
        if not cls.NEO4J_PASSWORD or cls.NEO4J_PASSWORD == "password":
            required_vars.append("NEO4J_PASSWORD (using default 'password')")

        # Supabase is optional for basic operation but required for registry
        # Check based on current branch
        current_branch = cls.get_current_branch()
        if current_branch == "develop":
            if not os.getenv("SUPABASE_URL_DEVELOP"):
                required_vars.append("SUPABASE_URL_DEVELOP (required for global registry on develop branch)")
            if not os.getenv("SUPABASE_KEY_DEVELOP"):
                required_vars.append("SUPABASE_KEY_DEVELOP (required for global registry on develop branch)")
        else:
            if not os.getenv("SUPABASE_URL"):
                required_vars.append("SUPABASE_URL (required for global registry)")
            if not os.getenv("SUPABASE_KEY"):
                required_vars.append("SUPABASE_KEY (required for global registry)")

        return required_vars
    
    @classmethod
    def get_checkpoint_path(cls, source: str) -> str:
        """Get checkpoint path for a specific source."""
        checkpoint_map = {
            "moniteur": cls.MONITEUR_CHECKPOINT,
            "vlaamse": cls.VLAAMSE_CHECKPOINT,
            "eurlex": cls.EURLEX_CHECKPOINT,
            "cellar": cls.CELLAR_CHECKPOINT
        }
        return checkpoint_map.get(source, str(cls.CHECKPOINTS_DIR / f"{source}.progress"))


# Global config instance
config = Config()

# Initialize Supabase configuration based on current branch
config.SUPABASE_URL, config.SUPABASE_KEY = config.get_supabase_config()


def setup_logging():
    """Set up logging configuration."""
    import logging
    
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL.upper()),
        format=config.LOG_FORMAT,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(config.LOGS_DIR / "ailex-be-ingest.log")
        ]
    )
    
    # Set specific loggers to appropriate levels
    logging.getLogger("neo4j").setLevel(logging.WARNING)
    logging.getLogger("pinecone").setLevel(logging.WARNING)
    logging.getLogger("voyageai").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
