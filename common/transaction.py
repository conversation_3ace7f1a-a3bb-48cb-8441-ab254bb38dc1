"""
Transactional ingestion patterns for ailex-be-ingest.

Provides atomic per-document ingestion with proper rollback on failure.
Ensures either a document is fully processed or nothing is committed.
"""

import logging
from typing import List, Dict, Any, Optional, Callable
from contextlib import contextmanager
from dataclasses import dataclass

import tiktoken
import voyageai
from pinecone import Pinecone
from google.cloud import storage
from neo4j import Session

from .models import CommonAct, CommonArticle, create_vector_id, PINECONE_NAMESPACES
from .neo4j import Neo4jClient
from .config import config
from .checkpoint import Checkpoint
from .registry import GlobalRegistry, RegistryEntry, calculate_sha256, create_gcs_uri

logger = logging.getLogger(__name__)


@dataclass
class IngestionResult:
    """Result of document ingestion."""
    success: bool
    act_id: str
    articles_count: int = 0
    vectors_count: int = 0
    files_saved: int = 0
    error: Optional[str] = None


class TransactionalIngester:
    """
    Atomic document ingester with rollback capabilities.
    
    Ensures either complete success or complete rollback for each document.
    """
    
    def __init__(self,
                 source: str,
                 dry_run: bool = False,
                 save_raw_files: bool = True,
                 use_registry: bool = True):
        """
        Initialize transactional ingester.

        Args:
            source: Source identifier (vlaamse, eu_dump, eu_rest)
            dry_run: If True, don't write to external services
            save_raw_files: Whether to save raw files to GCS
            use_registry: Whether to use global registry for tracking
        """
        self.source = source
        self.dry_run = dry_run
        self.save_raw_files = save_raw_files
        self.use_registry = use_registry

        # Initialize tokenizer
        self.tokenizer = tiktoken.get_encoding("cl100k_base")

        # Initialize services (only if not dry run)
        if not dry_run:
            self._setup_services()
    
    def _setup_services(self):
        """Initialize external services."""
        # Voyage AI for embeddings
        self.voyage_client = voyageai.Client(api_key=config.VOYAGE_API_KEY)
        
        # Pinecone for vector storage
        pc = Pinecone(api_key=config.PINECONE_API_KEY)
        self.pinecone_index = pc.Index(config.PINECONE_INDEX)
        
        # Neo4j for knowledge graph
        self.neo4j_client = Neo4jClient()
        
        # Google Cloud Storage for raw files
        if self.save_raw_files and config.GCS_BUCKET:
            self.gcs_client = storage.Client()
            self.gcs_bucket = self.gcs_client.bucket(config.GCS_BUCKET)
        else:
            self.gcs_client = None
            self.gcs_bucket = None

        # Global Registry for tracking
        if self.use_registry:
            try:
                self.registry = GlobalRegistry()
            except Exception as e:
                logger.warning(f"Failed to initialize registry: {e}")
                self.registry = None
        else:
            self.registry = None
    
    def ingest_document_atomic(self,
                              act: CommonAct,
                              articles: List[CommonArticle],
                              raw_content: Optional[str] = None,
                              raw_metadata: Optional[Dict[str, Any]] = None) -> IngestionResult:
        """
        Atomically ingest a single document with registry tracking.

        Either all operations succeed or all are rolled back.

        Args:
            act: CommonAct instance
            articles: List of CommonArticle instances
            raw_content: Raw file content (XML, etc.)
            raw_metadata: Additional metadata for raw file storage

        Returns:
            IngestionResult with success status and counts
        """
        if self.dry_run:
            return self._dry_run_ingest(act, articles)

        logger.debug(f"Starting atomic ingestion for {act.id}")

        # Calculate SHA-256 if raw content provided
        sha256 = None
        if raw_content:
            sha256 = calculate_sha256(raw_content)

        # Check registry if enabled
        if self.registry and sha256:
            if self.registry.exists(self.source, act.id, sha256):
                logger.info(f"Document {act.id} already fully processed (registry check)")
                return IngestionResult(
                    success=True,
                    act_id=act.id,
                    articles_count=len(articles),
                    vectors_count=0,  # Already processed
                    files_saved=0
                )

        try:
            # Use Neo4j transaction for atomicity
            with self.neo4j_client.driver.session() as session:
                with session.begin_transaction() as tx:
                    result = self._ingest_with_transaction(
                        tx, act, articles, raw_content, raw_metadata, sha256
                    )

                    if result.success:
                        tx.commit()
                        logger.info(f"Successfully ingested {act.id}: {result.articles_count} articles, {result.vectors_count} vectors")
                    else:
                        tx.rollback()
                        logger.error(f"Ingestion failed for {act.id}: {result.error}")

                    return result

        except Exception as e:
            logger.error(f"Atomic ingestion failed for {act.id}: {e}")
            return IngestionResult(
                success=False,
                act_id=act.id,
                error=str(e)
            )
    
    def _ingest_with_transaction(self,
                               tx: Session,
                               act: CommonAct,
                               articles: List[CommonArticle],
                               raw_content: Optional[str],
                               raw_metadata: Optional[Dict[str, Any]],
                               sha256: Optional[str]) -> IngestionResult:
        """Perform ingestion within a Neo4j transaction."""
        
        try:
            # Step 1: Create initial registry entry (if enabled)
            gcs_uri = None
            if self.registry:
                if self.save_raw_files and raw_content:
                    gcs_uri = create_gcs_uri(self.source, act.id, act.date.year)

                registry_entry = RegistryEntry(
                    source=self.source,
                    doc_id=act.id,
                    gcs_uri=gcs_uri,
                    sha256=sha256,
                    neo4j_loaded=False,
                    pinecone_loaded=False,
                    article_count=len(articles)
                )

                if not self.registry.upsert(registry_entry):
                    raise Exception("Failed to create registry entry")

            # Step 2: Save raw files to GCS (if enabled)
            files_saved = 0
            if self.save_raw_files and raw_content:
                self._save_raw_files(act, raw_content, raw_metadata)
                files_saved = 1

            # Step 3: Upsert act in Neo4j (within transaction)
            self._upsert_act_tx(tx, act)

            # Step 4: Upsert articles in Neo4j (within transaction)
            for article in articles:
                self._upsert_article_tx(tx, article)

            # Step 5: Update registry Neo4j flag
            if self.registry:
                if not self.registry.update_flags(self.source, act.id, neo4j_loaded=True):
                    raise Exception("Failed to update registry Neo4j flag")

            # Step 6: Create and upsert vectors to Pinecone
            vectors_count = self._create_and_upsert_vectors(act, articles)

            # Step 7: Update registry Pinecone flag (final step)
            if self.registry:
                if not self.registry.update_flags(self.source, act.id, pinecone_loaded=True, article_count=vectors_count):
                    raise Exception("Failed to update registry Pinecone flag")

            return IngestionResult(
                success=True,
                act_id=act.id,
                articles_count=len(articles),
                vectors_count=vectors_count,
                files_saved=files_saved
            )

        except Exception as e:
            logger.error(f"Transaction step failed: {e}")
            # Registry entry will remain with flags=false, indicating incomplete processing
            raise  # Will trigger rollback
    
    def _save_raw_files(self, act: CommonAct, content: str, metadata: Optional[Dict[str, Any]]):
        """Save raw files to Google Cloud Storage."""
        if not self.gcs_bucket:
            return
        
        try:
            # Create file path: raw/{source}/{year}/{act_id}.{ext}
            year = act.date.year
            file_extension = self._get_file_extension(metadata)
            file_path = f"raw/{self.source}/{year}/{act.id}.{file_extension}"
            
            # Upload content
            blob = self.gcs_bucket.blob(file_path)
            blob.upload_from_string(content, content_type=self._get_content_type(file_extension))
            
            logger.debug(f"Saved raw file to GCS: {file_path}")
            
            # Also save metadata if provided
            if metadata:
                metadata_path = f"raw/{self.source}/{year}/{act.id}.metadata.json"
                metadata_blob = self.gcs_bucket.blob(metadata_path)
                import json
                metadata_blob.upload_from_string(
                    json.dumps(metadata, indent=2, default=str),
                    content_type='application/json'
                )
        
        except Exception as e:
            logger.error(f"Failed to save raw files for {act.id}: {e}")
            raise
    
    def _upsert_act_tx(self, tx: Session, act: CommonAct):
        """Upsert act within Neo4j transaction."""
        from .models import NEO4J_LABELS
        
        query = f"""
        MERGE (a:{NEO4J_LABELS['ACT']} {{id: $id}})
        SET a.title = $title,
            a.date = $date,
            a.language = $language,
            a.source = $source,
            a.eli = $eli,
            a.metadata = $metadata,
            a.updated_at = datetime()
        RETURN a.id as id
        """
        
        result = tx.run(query, {
            "id": act.id,
            "title": act.title,
            "date": act.date.isoformat(),
            "language": act.language,
            "source": act.source,
            "eli": act.eli,
            "metadata": act.metadata
        })
        
        if not result.single():
            raise Exception(f"Failed to upsert act {act.id}")
    
    def _upsert_article_tx(self, tx: Session, article: CommonArticle):
        """Upsert article within Neo4j transaction."""
        from .models import NEO4J_LABELS, NEO4J_RELATIONSHIPS
        
        query = f"""
        MERGE (art:{NEO4J_LABELS['ARTICLE']} {{id: $id}})
        SET art.act_id = $act_id,
            art.number = $number,
            art.heading = $heading,
            art.text = $text,
            art.language = $language,
            art.metadata = $metadata,
            art.updated_at = datetime()
        
        WITH art
        MATCH (act:{NEO4J_LABELS['ACT']} {{id: $act_id}})
        MERGE (act)-[:{NEO4J_RELATIONSHIPS['HAS_ARTICLE']}]->(art)
        
        RETURN art.id as id
        """
        
        result = tx.run(query, {
            "id": article.id,
            "act_id": article.act_id,
            "number": article.number,
            "heading": article.heading,
            "text": article.text,
            "language": article.language,
            "metadata": article.metadata
        })
        
        if not result.single():
            raise Exception(f"Failed to upsert article {article.id}")
    
    def _create_and_upsert_vectors(self, act: CommonAct, articles: List[CommonArticle]) -> int:
        """Create embeddings and upsert to Pinecone with deterministic IDs."""
        if not articles:
            return 0
        
        try:
            # Chunk article texts
            all_chunks = []
            chunk_metadata = []
            
            for article in articles:
                chunks = self._chunk_text(article.text)
                for chunk_index, chunk in enumerate(chunks):
                    # Create deterministic vector ID
                    vector_id = create_vector_id(
                        source=self.source,
                        act_id=act.id,
                        article_id=article.id,
                        chunk_index=chunk_index
                    )
                    
                    all_chunks.append(chunk)
                    chunk_metadata.append({
                        'vector_id': vector_id,
                        'act_id': act.id,
                        'article_id': article.id,
                        'article_number': article.number,
                        'chunk_index': chunk_index,
                        'language': article.language,
                        'source': act.source,
                        'title': act.title,
                        'date': act.date.isoformat(),
                        'eli': act.eli,
                        'text_preview': chunk[:200]
                    })
            
            if not all_chunks:
                return 0
            
            # Create embeddings
            result = self.voyage_client.embed(
                texts=all_chunks,
                model=config.VOYAGE_MODEL,
                output_dimension=config.VOYAGE_DIMENSION
            )
            
            # Prepare vectors for Pinecone with deterministic IDs
            vectors = []
            namespace = PINECONE_NAMESPACES[act.source]
            
            for chunk, embedding, metadata in zip(all_chunks, result.embeddings, chunk_metadata):
                vectors.append({
                    'id': metadata['vector_id'],  # Deterministic ID
                    'values': embedding,
                    'metadata': {k: v for k, v in metadata.items() if k != 'vector_id'}
                })
            
            # Upsert to Pinecone (idempotent)
            self.pinecone_index.upsert(vectors=vectors, namespace=namespace)
            
            logger.debug(f"Upserted {len(vectors)} vectors for {act.id}")
            return len(vectors)
        
        except Exception as e:
            logger.error(f"Failed to create vectors for {act.id}: {e}")
            raise
    
    def _chunk_text(self, text: str) -> List[str]:
        """Split text into overlapping chunks."""
        if not text or len(text.strip()) < 50:
            return []
        
        tokens = self.tokenizer.encode(text)
        chunks = []
        chunk_size = config.CHUNK_SIZE
        overlap = config.CHUNK_OVERLAP
        
        for i in range(0, len(tokens), chunk_size - overlap):
            chunk_tokens = tokens[i:i + chunk_size]
            if len(chunk_tokens) < 50:  # Skip very small chunks
                continue
            chunk_text = self.tokenizer.decode(chunk_tokens)
            chunks.append(chunk_text)
        
        return chunks
    
    def _dry_run_ingest(self, act: CommonAct, articles: List[CommonArticle]) -> IngestionResult:
        """Simulate ingestion for dry run."""
        logger.info(f"DRY RUN: Would ingest {act.id} with {len(articles)} articles")
        
        # Simulate chunking to get vector count
        total_chunks = 0
        for article in articles:
            chunks = self._chunk_text(article.text)
            total_chunks += len(chunks)
        
        return IngestionResult(
            success=True,
            act_id=act.id,
            articles_count=len(articles),
            vectors_count=total_chunks,
            files_saved=1 if self.save_raw_files else 0
        )
    
    def _get_file_extension(self, metadata: Optional[Dict[str, Any]]) -> str:
        """Get appropriate file extension based on metadata."""
        if not metadata:
            return "xml"
        
        # Check content type or URL for extension hints
        content_type = metadata.get('content_type', '')
        url = metadata.get('url', '')
        
        if 'xml' in content_type.lower() or url.endswith('.xml'):
            return 'xml'
        elif 'pdf' in content_type.lower() or url.endswith('.pdf'):
            return 'pdf'
        else:
            return 'xml'  # Default
    
    def _get_content_type(self, extension: str) -> str:
        """Get MIME type for file extension."""
        content_types = {
            'xml': 'application/xml',
            'pdf': 'application/pdf',
            'json': 'application/json',
            'txt': 'text/plain'
        }
        return content_types.get(extension, 'application/octet-stream')
    
    def close(self):
        """Clean up resources."""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()
