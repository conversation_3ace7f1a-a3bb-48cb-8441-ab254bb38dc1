"""
Neo4j integration for ailex-be-ingest.

Provides functions to upsert legal acts, articles, and relationships
into the Neo4j knowledge graph.
"""

import os
import json
import logging
from typing import List, Optional, Dict, Any
from datetime import date

from neo4j import GraphDatabase, Driver
from neo4j.exceptions import ServiceUnavailable, TransientError

from .config import config
from .models import CommonAct, CommonArticle, LegalRelationship, NEO4J_LABELS, NEO4J_RELATIONSHIPS

logger = logging.getLogger(__name__)


class Neo4jClient:
    """Neo4j client for legal document knowledge graph operations."""

    def __init__(self, uri: Optional[str] = None, username: Optional[str] = None, password: Optional[str] = None):
        """
        Initialize Neo4j client.

        Args:
            uri: Neo4j connection URI (defaults to NEO4J_URI env var)
            username: Neo4j username (defaults to NEO4J_USERNAME env var)
            password: Neo4j password (defaults to NEO4J_PASSWORD env var)
        """
        self.uri = uri or config.NEO4J_URI
        self.username = username or config.NEO4J_USERNAME
        self.password = password or config.NEO4J_PASSWORD

        self.driver: Optional[Driver] = None
        self._connect()

    @staticmethod
    def _serialize_metadata(metadata: dict) -> str:
        """
        Serialize metadata dictionary to JSON string for Neo4j storage.

        Args:
            metadata: Dictionary to serialize

        Returns:
            JSON string representation
        """
        if not metadata:
            return "{}"
        return json.dumps(metadata, default=str, ensure_ascii=False)
    
    def _connect(self):
        """Establish connection to Neo4j."""
        try:
            self.driver = GraphDatabase.driver(
                self.uri, 
                auth=(self.username, self.password),
                max_connection_lifetime=30 * 60,  # 30 minutes
                max_connection_pool_size=50,
                connection_acquisition_timeout=60  # 60 seconds
            )
            # Test connection
            with self.driver.session() as session:
                session.run("RETURN 1")
            logger.info(f"Connected to Neo4j at {self.uri}")
        except ServiceUnavailable as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise
    
    def close(self):
        """Close Neo4j connection."""
        if self.driver:
            self.driver.close()
            logger.info("Neo4j connection closed")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
    
    def upsert_act(self, act: CommonAct) -> bool:
        """
        Upsert a legal act into Neo4j.
        
        Args:
            act: CommonAct instance to upsert
            
        Returns:
            bool: True if successful, False otherwise
        """
        query = f"""
        MERGE (a:{NEO4J_LABELS['ACT']} {{id: $id}})
        SET a.title = $title,
            a.date = $date,
            a.language = $language,
            a.source = $source,
            a.eli = $eli,
            a.metadata = $metadata,
            a.updated_at = datetime()
        RETURN a.id as id
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(query, {
                    "id": act.id,
                    "title": act.title,
                    "date": act.date.isoformat(),
                    "language": act.language,
                    "source": act.source,
                    "eli": act.eli,
                    "metadata": self._serialize_metadata(act.metadata)
                })
                record = result.single()
                if record:
                    logger.debug(f"Upserted act: {record['id']}")
                    return True
                return False
        except Exception as e:
            logger.error(f"Failed to upsert act {act.id}: {e}")
            return False
    
    def upsert_article(self, article: CommonArticle) -> bool:
        """
        Upsert an article and create relationship to its parent act.
        
        Args:
            article: CommonArticle instance to upsert
            
        Returns:
            bool: True if successful, False otherwise
        """
        query = f"""
        MERGE (art:{NEO4J_LABELS['ARTICLE']} {{id: $id}})
        SET art.act_id = $act_id,
            art.number = $number,
            art.heading = $heading,
            art.text = $text,
            art.language = $language,
            art.metadata = $metadata,
            art.updated_at = datetime()
        
        WITH art
        MATCH (act:{NEO4J_LABELS['ACT']} {{id: $act_id}})
        MERGE (act)-[:{NEO4J_RELATIONSHIPS['HAS_ARTICLE']}]->(art)
        
        RETURN art.id as id
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(query, {
                    "id": article.id,
                    "act_id": article.act_id,
                    "number": article.number,
                    "heading": article.heading,
                    "text": article.text,
                    "language": article.language,
                    "metadata": self._serialize_metadata(article.metadata)
                })
                record = result.single()
                if record:
                    logger.debug(f"Upserted article: {record['id']}")
                    return True
                return False
        except Exception as e:
            logger.error(f"Failed to upsert article {article.id}: {e}")
            return False
    
    def create_relationship(self, relationship: LegalRelationship) -> bool:
        """
        Create a relationship between two legal acts.
        
        Args:
            relationship: LegalRelationship instance
            
        Returns:
            bool: True if successful, False otherwise
        """
        query = f"""
        MATCH (source:{NEO4J_LABELS['ACT']} {{id: $source_id}})
        MATCH (target:{NEO4J_LABELS['ACT']} {{id: $target_id}})
        MERGE (source)-[r:{relationship.relationship_type}]->(target)
        SET r.article_reference = $article_reference,
            r.metadata = $metadata,
            r.created_at = datetime()
        RETURN r
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(query, {
                    "source_id": relationship.source_act_id,
                    "target_id": relationship.target_act_id,
                    "article_reference": relationship.article_reference,
                    "metadata": self._serialize_metadata(relationship.metadata)
                })
                record = result.single()
                if record:
                    logger.debug(f"Created relationship: {relationship.source_act_id} -> {relationship.target_act_id}")
                    return True
                return False
        except Exception as e:
            logger.error(f"Failed to create relationship: {e}")
            return False
    
    def get_act_stats(self) -> Dict[str, int]:
        """Get statistics about acts in the database."""
        query = f"""
        MATCH (a:{NEO4J_LABELS['ACT']})
        RETURN a.source as source, count(a) as count
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(query)
                stats = {}
                for record in result:
                    stats[record["source"]] = record["count"]
                return stats
        except Exception as e:
            logger.error(f"Failed to get act stats: {e}")
            return {}
    
    def get_article_stats(self) -> Dict[str, int]:
        """Get statistics about articles in the database."""
        query = f"""
        MATCH (art:{NEO4J_LABELS['ARTICLE']})
        RETURN art.language as language, count(art) as count
        """
        
        try:
            with self.driver.session() as session:
                result = session.run(query)
                stats = {}
                for record in result:
                    stats[record["language"]] = record["count"]
                return stats
        except Exception as e:
            logger.error(f"Failed to get article stats: {e}")
            return {}


# Convenience functions for backward compatibility
def upsert_act(act: CommonAct, client: Optional[Neo4jClient] = None) -> bool:
    """Convenience function to upsert an act."""
    if client:
        return client.upsert_act(act)
    
    with Neo4jClient() as neo4j_client:
        return neo4j_client.upsert_act(act)


def upsert_article(article: CommonArticle, client: Optional[Neo4jClient] = None) -> bool:
    """Convenience function to upsert an article."""
    if client:
        return client.upsert_article(article)
    
    with Neo4jClient() as neo4j_client:
        return neo4j_client.upsert_article(article)
