#!/usr/bin/env python3
"""
Test Neo4j connection and schema setup.

Verifies that we can connect to Neo4j and that the schema is properly configured.
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from common.config import config
from common.neo4j import Neo4jClient
from common.models import CommonAct, CommonArticle
from datetime import date


def test_neo4j_connection():
    """Test basic Neo4j connection."""
    print("🔍 Testing Neo4j Connection...")
    
    try:
        with Neo4jClient() as client:
            # Test basic connection
            with client.driver.session() as session:
                result = session.run("RETURN 'Hello Neo4j!' as message")
                record = result.single()
                message = record["message"]
                
                if message == "Hello Neo4j!":
                    print("✅ Neo4j connection successful")
                    print(f"   URI: {config.NEO4J_URI}")
                    print(f"   Username: {config.NEO4J_USERNAME}")
                    return True
                else:
                    print("❌ Unexpected response from Neo4j")
                    return False
                    
    except Exception as e:
        print(f"❌ Neo4j connection failed: {e}")
        print(f"   URI: {config.NEO4J_URI}")
        print(f"   Username: {config.NEO4J_USERNAME}")
        return False


def test_neo4j_schema():
    """Test Neo4j schema and constraints."""
    print("🔍 Testing Neo4j Schema...")
    
    try:
        with Neo4jClient() as client:
            with client.driver.session() as session:
                # Check constraints
                result = session.run("SHOW CONSTRAINTS")
                constraints = [record["name"] for record in result]
                
                print(f"✅ Found {len(constraints)} constraints")
                for constraint in constraints:
                    print(f"   - {constraint}")
                
                # Check indexes
                result = session.run("SHOW INDEXES")
                indexes = [record["name"] for record in result]
                
                print(f"✅ Found {len(indexes)} indexes")
                for index in indexes:
                    print(f"   - {index}")
                
                return True
                
    except Exception as e:
        print(f"❌ Schema check failed: {e}")
        return False


def test_neo4j_operations():
    """Test basic Neo4j CRUD operations."""
    print("🔍 Testing Neo4j Operations...")
    
    try:
        with Neo4jClient() as client:
            # Test act upsert
            test_act = CommonAct(
                id="test_neo4j_act_001",
                title="Test Neo4j Act",
                date=date(2024, 1, 1),
                language="nl",
                source="vlaamse",
                eli="eli:test:neo4j:act:001"
            )
            
            success = client.upsert_act(test_act)
            if not success:
                print("❌ Failed to upsert test act")
                return False
            
            print("✅ Act upsert successful")
            
            # Test article upsert
            test_article = CommonArticle(
                id="test_neo4j_act_001#art1",
                act_id="test_neo4j_act_001",
                number="1",
                heading="Test Article",
                text="This is a test article for Neo4j connection testing.",
                language="nl"
            )
            
            success = client.upsert_article(test_article)
            if not success:
                print("❌ Failed to upsert test article")
                return False
            
            print("✅ Article upsert successful")
            
            # Test relationship creation
            success = client.create_relationship(test_act.id, test_article.id, "HAS_ARTICLE")
            if not success:
                print("❌ Failed to create relationship")
                return False
            
            print("✅ Relationship creation successful")
            
            # Test querying
            with client.driver.session() as session:
                result = session.run("""
                    MATCH (a:Act {id: $act_id})-[:HAS_ARTICLE]->(art:Article)
                    RETURN a.title, art.heading, art.number
                """, {"act_id": test_act.id})
                
                record = result.single()
                if record:
                    print(f"✅ Query successful: {record['a.title']} -> Article {record['art.number']}: {record['art.heading']}")
                else:
                    print("❌ Query returned no results")
                    return False
            
            # Clean up test data
            with client.driver.session() as session:
                session.run("""
                    MATCH (a:Act {id: $act_id})
                    DETACH DELETE a
                """, {"act_id": test_act.id})
                
                session.run("""
                    MATCH (art:Article {id: $article_id})
                    DELETE art
                """, {"article_id": test_article.id})
            
            print("✅ Test data cleaned up")
            return True
            
    except Exception as e:
        print(f"❌ Neo4j operations failed: {e}")
        return False


def test_neo4j_stats():
    """Test Neo4j statistics functions."""
    print("🔍 Testing Neo4j Statistics...")
    
    try:
        with Neo4jClient() as client:
            # Get act stats
            act_stats = client.get_act_stats()
            print(f"✅ Act statistics: {act_stats}")
            
            # Get article stats
            article_stats = client.get_article_stats()
            print(f"✅ Article statistics: {article_stats}")
            
            return True
            
    except Exception as e:
        print(f"❌ Statistics failed: {e}")
        return False


def show_neo4j_info():
    """Show Neo4j database information."""
    print("🔍 Neo4j Database Information...")
    
    try:
        with Neo4jClient() as client:
            with client.driver.session() as session:
                # Get database info
                result = session.run("CALL dbms.components() YIELD name, versions, edition")
                for record in result:
                    print(f"✅ {record['name']}: {record['versions'][0]} ({record['edition']})")
                
                # Get database size
                result = session.run("""
                    MATCH (n)
                    RETURN labels(n) as labels, count(n) as count
                    ORDER BY count DESC
                """)
                
                print("📊 Node counts by label:")
                total_nodes = 0
                for record in result:
                    labels = record["labels"]
                    count = record["count"]
                    total_nodes += count
                    if labels:
                        print(f"   {':'.join(labels)}: {count}")
                    else:
                        print(f"   (no labels): {count}")
                
                print(f"📈 Total nodes: {total_nodes}")
                
                # Get relationship counts
                result = session.run("""
                    MATCH ()-[r]->()
                    RETURN type(r) as relationship_type, count(r) as count
                    ORDER BY count DESC
                """)
                
                print("🔗 Relationship counts by type:")
                total_relationships = 0
                for record in result:
                    rel_type = record["relationship_type"]
                    count = record["count"]
                    total_relationships += count
                    print(f"   {rel_type}: {count}")
                
                print(f"📈 Total relationships: {total_relationships}")
                
                return True
                
    except Exception as e:
        print(f"❌ Database info failed: {e}")
        return False


def main():
    """Run all Neo4j tests."""
    print("🧪 Testing Neo4j Connection & Operations")
    print("=" * 50)
    
    tests = [
        test_neo4j_connection,
        test_neo4j_schema,
        test_neo4j_operations,
        test_neo4j_stats,
        show_neo4j_info
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed with exception: {e}")
            failed += 1
        print()
    
    print("📊 Test Results")
    print("=" * 20)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All Neo4j tests passed!")
        print("✅ Neo4j is ready for production use")
    else:
        print(f"\n⚠️  {failed} test(s) failed")
        print("🔧 Please check your Neo4j configuration and connection")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
