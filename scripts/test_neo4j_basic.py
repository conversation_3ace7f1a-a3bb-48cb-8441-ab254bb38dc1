#!/usr/bin/env python3
"""
Basic Neo4j connection test.
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from common.config import config
from neo4j import GraphDatabase


def test_basic_connection():
    """Test basic Neo4j connection with detailed error handling."""
    print("🔍 Testing Basic Neo4j Connection...")
    print(f"URI: {config.NEO4J_URI}")
    print(f"Username: {config.NEO4J_USERNAME}")
    print(f"Password: {'*' * len(config.NEO4J_PASSWORD)}")
    
    try:
        # Create driver
        driver = GraphDatabase.driver(
            config.NEO4J_URI,
            auth=(config.NEO4J_USERNAME, config.NEO4J_PASSWORD)
        )
        
        # Test connection with a simple query
        with driver.session() as session:
            result = session.run("RETURN 1 as test")
            record = result.single()
            test_value = record["test"]
            
            if test_value == 1:
                print("✅ Neo4j connection successful!")
                
                # Get some basic info
                result = session.run("CALL dbms.components() YIELD name, versions, edition")
                for record in result:
                    print(f"   {record['name']}: {record['versions'][0]} ({record['edition']})")
                
                driver.close()
                return True
            else:
                print("❌ Unexpected response from Neo4j")
                driver.close()
                return False
                
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        
        # Provide specific guidance based on error type
        error_str = str(e).lower()
        if "unauthorized" in error_str or "authentication" in error_str:
            print("\n💡 Authentication Issue Detected:")
            print("   1. Check if the password is correct in your .env file")
            print("   2. Verify the database is running in Neo4j Aura Console")
            print("   3. Try resetting the password in Neo4j Aura")
            print("   4. Wait a few minutes if you've had multiple failed attempts")
        elif "service unavailable" in error_str:
            print("\n💡 Service Unavailable:")
            print("   1. Check if the database is paused in Neo4j Aura")
            print("   2. Verify the URI is correct")
            print("   3. Check your internet connection")
        
        return False


def main():
    """Run basic connection test."""
    print("🧪 Basic Neo4j Connection Test")
    print("=" * 40)
    
    success = test_basic_connection()
    
    if success:
        print("\n🎉 Neo4j connection working!")
        print("✅ Ready to proceed with full testing")
    else:
        print("\n⚠️  Neo4j connection failed")
        print("🔧 Please check the guidance above")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
