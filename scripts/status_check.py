#!/usr/bin/env python3
"""
Status check script for ailex-be-ingest.

Verifies the current implementation status and provides guidance
on next steps.
"""

import sys
import os
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from common.config import config


def check_environment():
    """Check environment configuration."""
    print("🔧 Environment Configuration")
    print("=" * 40)
    
    # Check required variables
    required_vars = config.validate()
    if required_vars:
        print("❌ Missing required environment variables:")
        for var in required_vars:
            print(f"   - {var}")
        return False
    else:
        print("✅ All required environment variables are set")
    
    # Check optional services
    optional_checks = [
        ("Google Cloud Storage", config.GCS_BUCKET and config.GCS_CREDENTIALS_PATH),
        ("Hugging Face Token", bool(config.HF_TOKEN)),
        ("EUR-Lex Credentials", config.EURLEX_USERNAME and config.EURLEX_PASSWORD)
    ]
    
    print("\n📋 Optional Services:")
    for service, available in optional_checks:
        status = "✅" if available else "⚠️ "
        print(f"   {status} {service}")
    
    return True


def check_infrastructure():
    """Check infrastructure connectivity."""
    print("\n🏗️  Infrastructure Connectivity")
    print("=" * 40)
    
    # Check Neo4j
    try:
        from common.neo4j import Neo4jClient
        with Neo4jClient() as client:
            stats = client.get_act_stats()
        print("✅ Neo4j: Connected")
        if stats:
            print(f"   📊 Acts in database: {sum(stats.values())}")
    except Exception as e:
        print(f"❌ Neo4j: Failed to connect ({e})")
        return False
    
    # Check Pinecone
    try:
        from pinecone import Pinecone
        pc = Pinecone(api_key=config.PINECONE_API_KEY)
        index = pc.Index(config.PINECONE_INDEX)
        stats = index.describe_index_stats()
        print("✅ Pinecone: Connected")
        print(f"   📊 Total vectors: {stats.total_vector_count}")
    except Exception as e:
        print(f"❌ Pinecone: Failed to connect ({e})")
        return False
    
    # Check Voyage AI
    try:
        import voyageai
        client = voyageai.Client(api_key=config.VOYAGE_API_KEY)
        # Test with a simple embedding
        result = client.embed(texts=["test"], model="voyage-3-large")
        print("✅ Voyage AI: Connected")
    except Exception as e:
        print(f"❌ Voyage AI: Failed to connect ({e})")
        return False
    
    return True


def check_implementation_status():
    """Check implementation status of each phase."""
    print("\n📈 Implementation Status")
    print("=" * 40)
    
    phases = [
        {
            "name": "Phase A - Vlaamse Codex",
            "status": "✅ COMPLETE",
            "components": [
                ("Unified Data Models", True),
                ("API Client", True),
                ("XML Parser", True),
                ("Neo4j Integration", True),
                ("Ingestion Pipeline", True),
                ("Unit Tests", True)
            ]
        },
        {
            "name": "Phase B - EUR-Lex Bulk",
            "status": "🚧 PLANNED",
            "components": [
                ("Bulk Downloader", False),
                ("FORMEX Parser", False),
                ("Model Mapping", False),
                ("Pipeline Integration", False),
                ("Unit Tests", False)
            ]
        },
        {
            "name": "Phase C - CELLAR SPARQL",
            "status": "🚧 PLANNED", 
            "components": [
                ("SPARQL Client", False),
                ("REST Integration", False),
                ("Delta Updates", False),
                ("Pipeline Integration", False),
                ("Unit Tests", False)
            ]
        }
    ]
    
    for phase in phases:
        print(f"\n{phase['status']} {phase['name']}")
        for component, implemented in phase['components']:
            status = "✅" if implemented else "⭕"
            print(f"   {status} {component}")


def show_next_steps():
    """Show recommended next steps."""
    print("\n🚀 Recommended Next Steps")
    print("=" * 40)
    
    steps = [
        "1. Test Phase A with real data:",
        "   python pipelines/initial_load.py --dry-run --limit 5 --vlaamse-only",
        "",
        "2. Run actual ingestion (small batch):",
        "   python pipelines/initial_load.py --limit 10 --vlaamse-only",
        "",
        "3. Verify data in Neo4j:",
        "   # Open http://localhost:7474",
        "   # Run: MATCH (a:Act) RETURN a.source, count(a)",
        "",
        "4. Implement Phase B (EUR-Lex):",
        "   - Create sources/eurlex/ module",
        "   - Implement bulk download client",
        "   - Build FORMEX XML parser",
        "",
        "5. Implement Phase C (CELLAR):",
        "   - Create sources/cellar/ module", 
        "   - Implement SPARQL client",
        "   - Build delta update mechanism"
    ]
    
    for step in steps:
        print(step)


def main():
    """Main status check function."""
    print("🏛️ AiLex-BE-Ingest Status Check")
    print("=" * 50)
    
    # Check environment
    env_ok = check_environment()
    
    # Check infrastructure (only if env is OK)
    infra_ok = check_infrastructure() if env_ok else False
    
    # Show implementation status
    check_implementation_status()
    
    # Show next steps
    show_next_steps()
    
    # Summary
    print("\n📋 Summary")
    print("=" * 40)
    
    if env_ok and infra_ok:
        print("✅ System is ready for Phase A ingestion!")
        print("✅ All infrastructure components are working")
        print("🚀 You can start ingesting Vlaamse Codex documents")
    elif env_ok:
        print("⚠️  Environment is configured but infrastructure needs setup")
        print("🔧 Please check Neo4j, Pinecone, and Voyage AI connections")
    else:
        print("❌ Environment configuration incomplete")
        print("🔧 Please set required environment variables in .env file")
    
    return env_ok and infra_ok


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
