#!/usr/bin/env python3
"""
Test script for checkpoint and idempotency functionality.

Verifies that:
1. Interrupting ingestion and restarting produces no duplicates
2. Vector IDs are deterministic
3. Checkpoint state is properly maintained
"""

import sys
import time
import signal
import subprocess
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from common.config import config, setup_logging
from common.checkpoint import Checkpoint
from common.models import create_vector_id, parse_vector_id
from common.neo4j import Neo4jClient

setup_logging()


def test_vector_id_determinism():
    """Test that vector IDs are deterministic."""
    print("🔍 Testing vector ID determinism...")
    
    # Same inputs should produce same IDs
    id1 = create_vector_id("vlaamse", "act123", "art456", 1)
    id2 = create_vector_id("vlaamse", "act123", "art456", 1)
    assert id1 == id2, f"Expected same IDs: {id1} != {id2}"
    
    # Different inputs should produce different IDs
    id3 = create_vector_id("vlaamse", "act123", "art456", 2)
    assert id1 != id3, f"Expected different IDs: {id1} == {id3}"
    
    # Test parsing
    parsed = parse_vector_id(id1)
    assert parsed["source"] == "vlaamse"
    assert parsed["act_id"] == "act123"
    assert parsed["article_id"] == "art456"
    assert parsed["chunk_index"] == 1
    
    print("✅ Vector ID determinism test passed")


def test_checkpoint_functionality():
    """Test checkpoint read/write functionality."""
    print("🔍 Testing checkpoint functionality...")
    
    checkpoint = Checkpoint("test_checkpoint")
    
    # Clean start
    if checkpoint.exists():
        checkpoint.delete()
    
    # Test initial state
    data = checkpoint.read()
    assert data == {}, f"Expected empty checkpoint, got: {data}"
    
    # Test write and read
    test_data = {
        "cursor": "2024-01-01T00:00:00Z",
        "doc_id": "test123",
        "count": 42
    }
    
    checkpoint.write(test_data)
    assert checkpoint.exists(), "Checkpoint should exist after write"
    
    # Test read with metadata
    read_data = checkpoint.read()
    assert read_data["cursor"] == test_data["cursor"]
    assert read_data["doc_id"] == test_data["doc_id"]
    assert read_data["count"] == test_data["count"]
    assert "updated_at" in read_data
    assert "version" in read_data
    
    # Test convenience methods
    cursor = checkpoint.get_cursor()
    assert cursor == test_data["cursor"]
    
    # Test update
    checkpoint.update_cursor("2024-01-02T00:00:00Z", extra="value")
    new_cursor = checkpoint.get_cursor()
    assert new_cursor == "2024-01-02T00:00:00Z"
    
    new_data = checkpoint.read()
    assert new_data["extra"] == "value"
    
    # Test stats
    stats = checkpoint.get_stats()
    assert stats["source"] == "test_checkpoint"
    assert stats["exists"] is True
    
    # Clean up
    checkpoint.delete()
    assert not checkpoint.exists(), "Checkpoint should not exist after delete"
    
    print("✅ Checkpoint functionality test passed")


def test_neo4j_idempotency():
    """Test that Neo4j operations are idempotent."""
    print("🔍 Testing Neo4j idempotency...")
    
    try:
        with Neo4jClient() as client:
            # Test act upsert idempotency
            from common.models import CommonAct
            from datetime import date
            
            act = CommonAct(
                id="test_idempotent_act",
                title="Test Idempotent Act",
                date=date(2024, 1, 1),
                language="nl",
                source="vlaamse"
            )
            
            # First upsert
            success1 = client.upsert_act(act)
            assert success1, "First act upsert should succeed"
            
            # Second upsert (should be idempotent)
            success2 = client.upsert_act(act)
            assert success2, "Second act upsert should succeed (idempotent)"
            
            # Verify only one act exists
            with client.driver.session() as session:
                result = session.run(
                    "MATCH (a:Act {id: $id}) RETURN count(a) as count",
                    {"id": act.id}
                )
                count = result.single()["count"]
                assert count == 1, f"Expected 1 act, found {count}"
            
            # Clean up
            with client.driver.session() as session:
                session.run("MATCH (a:Act {id: $id}) DELETE a", {"id": act.id})
            
            print("✅ Neo4j idempotency test passed")
    
    except Exception as e:
        print(f"⚠️  Neo4j idempotency test skipped: {e}")


def test_resume_simulation():
    """Simulate interruption and resume scenario."""
    print("🔍 Testing resume simulation...")
    
    checkpoint = Checkpoint("vlaamse")
    
    # Clean start
    if checkpoint.exists():
        checkpoint.delete()
    
    # Simulate first run with some progress
    print("📝 Simulating first run...")
    checkpoint.write({
        "cursor": "2024-01-15T00:00:00Z",
        "last_document_id": "doc_123",
        "documents_processed": 25
    })
    
    # Verify checkpoint exists
    assert checkpoint.exists(), "Checkpoint should exist after first run"
    
    # Simulate second run resuming from checkpoint
    print("🔄 Simulating resume...")
    data = checkpoint.read()
    assert data["cursor"] == "2024-01-15T00:00:00Z"
    assert data["documents_processed"] == 25
    
    # Simulate more progress
    checkpoint.write({
        "cursor": "2024-01-20T00:00:00Z",
        "last_document_id": "doc_456",
        "documents_processed": 50
    })
    
    # Verify updated state
    final_data = checkpoint.read()
    assert final_data["cursor"] == "2024-01-20T00:00:00Z"
    assert final_data["documents_processed"] == 50
    
    # Clean up
    checkpoint.delete()
    
    print("✅ Resume simulation test passed")


def test_cli_flags():
    """Test CLI flags for checkpoint management."""
    print("🔍 Testing CLI flags...")
    
    try:
        # Test status flag
        result = subprocess.run([
            sys.executable, "sources/vlaamse_codex/ingest.py", 
            "--status", "--dry-run"
        ], capture_output=True, text=True, timeout=30)
        
        assert result.returncode == 0, f"Status command failed: {result.stderr}"
        assert "Checkpoint Status" in result.stdout
        
        # Test reset flag
        result = subprocess.run([
            sys.executable, "sources/vlaamse_codex/ingest.py",
            "--reset", "--dry-run", "--limit", "1"
        ], capture_output=True, text=True, timeout=30)
        
        assert result.returncode == 0, f"Reset command failed: {result.stderr}"
        
        print("✅ CLI flags test passed")
    
    except subprocess.TimeoutExpired:
        print("⚠️  CLI flags test timed out")
    except Exception as e:
        print(f"⚠️  CLI flags test failed: {e}")


def main():
    """Run all checkpoint tests."""
    print("🧪 Testing Checkpoint & Idempotency Layer")
    print("=" * 50)
    
    tests = [
        test_vector_id_determinism,
        test_checkpoint_functionality,
        test_neo4j_idempotency,
        test_resume_simulation,
        test_cli_flags
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed: {e}")
            failed += 1
        print()
    
    print("📊 Test Results")
    print("=" * 20)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All checkpoint tests passed!")
        print("✅ System is ready for production use")
    else:
        print(f"\n⚠️  {failed} test(s) failed")
        print("🔧 Please fix issues before production use")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
