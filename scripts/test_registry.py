#!/usr/bin/env python3
"""
Test script for global registry functionality.

Verifies that the registry system works correctly with mock data.
"""

import sys
import logging
from datetime import datetime, date
from pathlib import Path
from unittest.mock import Mock, patch

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from common.config import config, setup_logging
from common.registry import GlobalRegistry, RegistryEntry, calculate_sha256, create_gcs_uri
from tools.audit import ConsistencyAuditor, AuditStatus
from tools.repair import RepairTool

setup_logging()
logger = logging.getLogger(__name__)


def test_registry_operations():
    """Test basic registry operations with mocked Supabase."""
    print("🔍 Testing Registry Operations...")
    
    # Mock Supabase client
    with patch('common.registry.create_client') as mock_create_client:
        mock_client = Mock()
        mock_table = Mock()
        mock_query = Mock()
        
        mock_create_client.return_value = mock_client
        mock_client.table.return_value = mock_table
        mock_table.select.return_value = mock_query
        mock_table.upsert.return_value = mock_table
        mock_table.update.return_value = mock_table
        mock_table.eq.return_value = mock_query
        mock_query.eq.return_value = mock_query
        
        # Test successful operations
        mock_query.execute.return_value.data = []  # No existing entries
        mock_table.execute.return_value.data = [{"id": "test_id"}]
        
        try:
            registry = GlobalRegistry("http://test.supabase.co", "test_key")
            
            # Test exists check
            exists = registry.exists("vlaamse", "test_doc", "abc123")
            assert exists is False, "Should not exist initially"
            
            # Test upsert
            entry = RegistryEntry(
                source="vlaamse",
                doc_id="test_doc",
                gcs_uri="gs://bucket/test.xml",
                sha256="abc123",
                neo4j_loaded=True,
                pinecone_loaded=False,
                article_count=5
            )
            
            success = registry.upsert(entry)
            assert success is True, "Upsert should succeed"
            
            # Test update flags
            success = registry.update_flags("vlaamse", "test_doc", pinecone_loaded=True)
            assert success is True, "Update flags should succeed"
            
            print("✅ Registry operations test passed")
            return True
            
        except Exception as e:
            print(f"❌ Registry operations test failed: {e}")
            return False


def test_utility_functions():
    """Test utility functions."""
    print("🔍 Testing Utility Functions...")
    
    try:
        # Test SHA-256 calculation
        content = "test content for hashing"
        sha256 = calculate_sha256(content)
        
        assert len(sha256) == 64, "SHA-256 should be 64 characters"
        assert sha256 == calculate_sha256(content), "SHA-256 should be deterministic"
        
        # Test GCS URI creation
        uri = create_gcs_uri("vlaamse", "test_act_123", 2024, "xml")
        expected = "gs://ailex-be/raw/vlaamse/2024/test_act_123.xml"
        assert uri == expected, f"Expected {expected}, got {uri}"
        
        print("✅ Utility functions test passed")
        return True
        
    except Exception as e:
        print(f"❌ Utility functions test failed: {e}")
        return False


def test_audit_tool():
    """Test audit tool with mocked dependencies."""
    print("🔍 Testing Audit Tool...")
    
    try:
        with patch('tools.audit.GlobalRegistry') as mock_registry_class:
            with patch('tools.audit.Neo4jClient') as mock_neo4j_class:
                with patch('tools.audit.Pinecone') as mock_pinecone_class:
                    
                    # Setup mocks
                    mock_registry = Mock()
                    mock_registry.list_entries.return_value = [
                        RegistryEntry(
                            id="test_id",
                            source="vlaamse",
                            doc_id="test_123",
                            gcs_uri="gs://bucket/test.xml",
                            sha256="abc123",
                            neo4j_loaded=True,
                            pinecone_loaded=True,
                            article_count=5
                        )
                    ]
                    mock_registry_class.return_value = mock_registry
                    
                    mock_neo4j = Mock()
                    mock_driver = Mock()
                    mock_session = Mock()
                    mock_session.run.return_value.single.return_value = {"exists": True}
                    mock_driver.session.return_value.__enter__.return_value = mock_session
                    mock_neo4j.driver = mock_driver
                    mock_neo4j_class.return_value = mock_neo4j
                    
                    mock_pc = Mock()
                    mock_index = Mock()
                    mock_index.query.return_value.matches = [Mock()] * 5  # 5 vectors
                    mock_pc.Index.return_value = mock_index
                    mock_pinecone_class.return_value = mock_pc
                    
                    # Test audit
                    auditor = ConsistencyAuditor()
                    
                    with patch.object(auditor, '_check_gcs', return_value=(True, True)):
                        results = auditor.audit_entries(source="vlaamse", limit=10)
                    
                    assert len(results) == 1, "Should have one audit result"
                    result = results[0]
                    assert result.audit_status == AuditStatus.OK, "Should be consistent"
                    
                    # Test report generation
                    stats = auditor.generate_report(results, "test_audit_report.csv")
                    assert "OK" in stats, "Should have OK status in stats"
                    
                    print("✅ Audit tool test passed")
                    return True
                    
    except Exception as e:
        print(f"❌ Audit tool test failed: {e}")
        return False


def test_repair_tool():
    """Test repair tool with mocked dependencies."""
    print("🔍 Testing Repair Tool...")
    
    try:
        with patch('tools.repair.GlobalRegistry') as mock_registry_class:
            mock_registry = Mock()
            mock_registry.get_inconsistent_entries.return_value = [
                RegistryEntry(
                    id="test_id",
                    source="vlaamse",
                    doc_id="test_123",
                    gcs_uri="gs://bucket/test.xml",
                    neo4j_loaded=False,
                    pinecone_loaded=True,
                    article_count=5
                )
            ]
            mock_registry_class.return_value = mock_registry
            
            repair_tool = RepairTool(dry_run=True)
            
            with patch.object(repair_tool, '_download_from_gcs', return_value="<xml>test</xml>"):
                with patch.object(repair_tool, 'parsers', {"vlaamse": Mock()}):
                    repair_tool.parsers["vlaamse"].parse_document.return_value = (Mock(), [Mock()])
                    
                    results = repair_tool.repair_all_inconsistencies(source="vlaamse", limit=10)
            
            assert len(results) == 1, "Should have one repair result"
            result = results[0]
            assert result.success is True, "Repair should succeed in dry run"
            
            print("✅ Repair tool test passed")
            return True
            
    except Exception as e:
        print(f"❌ Repair tool test failed: {e}")
        return False


def test_transactional_integration():
    """Test transactional ingester with registry integration."""
    print("🔍 Testing Transactional Integration...")
    
    try:
        with patch('common.transaction.GlobalRegistry') as mock_registry_class:
            mock_registry = Mock()
            mock_registry.exists.return_value = False
            mock_registry.upsert.return_value = True
            mock_registry.update_flags.return_value = True
            mock_registry_class.return_value = mock_registry
            
            from common.transaction import TransactionalIngester
            from common.models import CommonAct, CommonArticle
            
            # Test dry run
            ingester = TransactionalIngester("vlaamse", dry_run=True, use_registry=True)
            
            act = CommonAct(
                id="test_act",
                title="Test Act",
                date=date(2024, 1, 1),
                language="nl",
                source="vlaamse"
            )
            
            articles = [
                CommonArticle(
                    id="test_act#art1",
                    act_id="test_act",
                    number="1",
                    text="Test article content",
                    language="nl"
                )
            ]
            
            result = ingester.ingest_document_atomic(act, articles, raw_content="<xml>test</xml>")
            
            assert result.success is True, "Ingestion should succeed"
            assert result.act_id == "test_act", "Should have correct act ID"
            
            print("✅ Transactional integration test passed")
            return True
            
    except Exception as e:
        print(f"❌ Transactional integration test failed: {e}")
        return False


def main():
    """Run all registry tests."""
    print("🧪 Testing Global Registry & Consistency Auditor")
    print("=" * 60)
    
    tests = [
        test_utility_functions,
        test_registry_operations,
        test_audit_tool,
        test_repair_tool,
        test_transactional_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed with exception: {e}")
            failed += 1
        print()
    
    print("📊 Test Results")
    print("=" * 20)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All registry tests passed!")
        print("✅ Global Registry & Consistency Auditor ready for production")
    else:
        print(f"\n⚠️  {failed} test(s) failed")
        print("🔧 Please fix issues before production use")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
