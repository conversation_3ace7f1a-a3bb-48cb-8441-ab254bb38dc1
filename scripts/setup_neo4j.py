#!/usr/bin/env python3
"""
Setup Neo4j database schema for ailex-be-ingest.

Creates indexes, constraints, and initial schema for the legal document
knowledge graph.
"""

import logging
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from common.config import config, setup_logging
from common.neo4j import Neo4j<PERSON>lient
from common.models import NEO4J_LABELS, NEO4J_RELATIONSHIPS

setup_logging()
logger = logging.getLogger(__name__)


def setup_neo4j_schema():
    """Set up Neo4j database schema with indexes and constraints."""
    
    logger.info("Setting up Neo4j schema...")
    
    try:
        with Neo4jClient() as client:
            # Create constraints for unique IDs
            constraints = [
                f"CREATE CONSTRAINT act_id_unique IF NOT EXISTS FOR (a:{NEO4J_LABELS['ACT']}) REQUIRE a.id IS UNIQUE",
                f"CREATE CONSTRAINT article_id_unique IF NOT EXISTS FOR (art:{NEO4J_LABELS['ARTICLE']}) REQUIRE art.id IS UNIQUE"
            ]
            
            # Create indexes for common queries
            indexes = [
                f"CREATE INDEX act_source_idx IF NOT EXISTS FOR (a:{NEO4J_LABELS['ACT']}) ON (a.source)",
                f"CREATE INDEX act_language_idx IF NOT EXISTS FOR (a:{NEO4J_LABELS['ACT']}) ON (a.language)",
                f"CREATE INDEX act_date_idx IF NOT EXISTS FOR (a:{NEO4J_LABELS['ACT']}) ON (a.date)",
                f"CREATE INDEX article_act_id_idx IF NOT EXISTS FOR (art:{NEO4J_LABELS['ARTICLE']}) ON (art.act_id)",
                f"CREATE INDEX article_language_idx IF NOT EXISTS FOR (art:{NEO4J_LABELS['ARTICLE']}) ON (art.language)",
                f"CREATE TEXT INDEX act_title_text_idx IF NOT EXISTS FOR (a:{NEO4J_LABELS['ACT']}) ON (a.title)",
                f"CREATE TEXT INDEX article_text_idx IF NOT EXISTS FOR (art:{NEO4J_LABELS['ARTICLE']}) ON (art.text)"
            ]
            
            # Execute constraints
            for constraint in constraints:
                try:
                    with client.driver.session() as session:
                        session.run(constraint)
                    logger.info(f"Created constraint: {constraint}")
                except Exception as e:
                    if "already exists" in str(e).lower():
                        logger.info(f"Constraint already exists: {constraint}")
                    else:
                        logger.error(f"Failed to create constraint: {e}")
                        raise
            
            # Execute indexes
            for index in indexes:
                try:
                    with client.driver.session() as session:
                        session.run(index)
                    logger.info(f"Created index: {index}")
                except Exception as e:
                    if "already exists" in str(e).lower():
                        logger.info(f"Index already exists: {index}")
                    else:
                        logger.error(f"Failed to create index: {e}")
                        raise
            
            # Verify schema
            with client.driver.session() as session:
                # Check constraints
                result = session.run("SHOW CONSTRAINTS")
                constraints_count = len(list(result))
                
                # Check indexes
                result = session.run("SHOW INDEXES")
                indexes_count = len(list(result))
                
                logger.info(f"Schema setup complete: {constraints_count} constraints, {indexes_count} indexes")
                
                # Get database stats
                result = session.run(f"MATCH (a:{NEO4J_LABELS['ACT']}) RETURN count(a) as act_count")
                act_count = result.single()["act_count"] if result.single() else 0
                
                result = session.run(f"MATCH (art:{NEO4J_LABELS['ARTICLE']}) RETURN count(art) as article_count")
                article_count = result.single()["article_count"] if result.single() else 0
                
                logger.info(f"Current database stats: {act_count} acts, {article_count} articles")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to setup Neo4j schema: {e}")
        return False


def verify_connection():
    """Verify Neo4j connection and basic functionality."""
    
    logger.info("Verifying Neo4j connection...")
    
    try:
        with Neo4jClient() as client:
            with client.driver.session() as session:
                result = session.run("RETURN 'Hello Neo4j!' as message")
                message = result.single()["message"]
                logger.info(f"Connection verified: {message}")
                
                # Check Neo4j version
                result = session.run("CALL dbms.components() YIELD name, versions")
                for record in result:
                    if record["name"] == "Neo4j Kernel":
                        version = record["versions"][0]
                        logger.info(f"Neo4j version: {version}")
                        break
        
        return True
        
    except Exception as e:
        logger.error(f"Neo4j connection failed: {e}")
        logger.error("Please ensure Neo4j is running and credentials are correct")
        logger.error(f"Connection details: {config.NEO4J_URI}, user: {config.NEO4J_USERNAME}")
        return False


def main():
    """Main setup function."""
    
    print("🚀 Setting up Neo4j for ailex-be-ingest")
    print("=" * 50)
    
    # Validate configuration
    missing_vars = config.validate()
    if missing_vars:
        logger.error(f"Missing required environment variables: {missing_vars}")
        return False
    
    # Verify connection
    if not verify_connection():
        return False
    
    # Setup schema
    if not setup_neo4j_schema():
        return False
    
    print("✅ Neo4j setup completed successfully!")
    print("\nNext steps:")
    print("1. Run the Vlaamse Codex ingestion pipeline")
    print("2. Verify data is being stored correctly")
    print("3. Test graph queries")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
