#!/usr/bin/env python3
"""
Test script for branch-aware configuration.

Verifies that the configuration system correctly detects the current
git branch and uses the appropriate Supabase settings.
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from common.config import config


def test_branch_detection():
    """Test git branch detection."""
    print("🔍 Testing Branch Detection...")
    
    current_branch = config.get_current_branch()
    print(f"Current branch: {current_branch}")
    
    if current_branch in ["main", "develop"]:
        print("✅ Branch detection working correctly")
        return True
    else:
        print(f"⚠️  Unexpected branch: {current_branch}")
        return True  # Still valid, just not main/develop


def test_supabase_config():
    """Test Supabase configuration selection."""
    print("🔍 Testing Supabase Configuration...")
    
    current_branch = config.get_current_branch()
    print(f"Current branch: {current_branch}")
    print(f"Supabase URL: {config.SUPABASE_URL[:50]}..." if config.SUPABASE_URL else "Supabase URL: Not set")
    print(f"Supabase Key: {'Set' if config.SUPABASE_KEY else 'Not set'}")
    
    # Test the configuration selection logic
    url, key = config.get_supabase_config()
    
    if current_branch == "develop":
        print("📍 Using DEVELOP Supabase configuration")
        if "yipgbsutqcpiiocsdtay" in url:
            print("✅ Correct develop Supabase URL detected")
        else:
            print("⚠️  Expected develop Supabase URL not found")
    else:
        print("📍 Using MAIN/PRODUCTION Supabase configuration")
        if "lsixcrtzawcxyfkxhyxd" in url:
            print("✅ Correct main Supabase URL detected")
        else:
            print("⚠️  Expected main Supabase URL not found")
    
    if url and key:
        print("✅ Supabase configuration loaded successfully")
        return True
    else:
        print("❌ Supabase configuration missing")
        return False


def test_config_validation():
    """Test configuration validation."""
    print("🔍 Testing Configuration Validation...")
    
    missing_vars = config.validate()
    
    if missing_vars:
        print("⚠️  Missing configuration variables:")
        for var in missing_vars:
            print(f"   - {var}")
        
        # Check if only optional variables are missing
        critical_missing = [var for var in missing_vars if "SUPABASE" in var or "VOYAGE" in var or "PINECONE" in var]
        if critical_missing:
            print("❌ Critical configuration missing")
            return False
        else:
            print("✅ Only optional configuration missing")
            return True
    else:
        print("✅ All required configuration present")
        return True


def test_environment_info():
    """Display environment information."""
    print("🔍 Environment Information...")
    
    print(f"Project Root: {config.PROJECT_ROOT}")
    print(f"Current Branch: {config.get_current_branch()}")
    print(f"Log Level: {config.LOG_LEVEL}")
    print(f"Chunk Size: {config.CHUNK_SIZE}")
    print(f"Voyage Model: {config.VOYAGE_MODEL}")
    print(f"Pinecone Index: {config.PINECONE_INDEX}")
    
    # Show which Supabase config is active
    current_branch = config.get_current_branch()
    if current_branch == "develop":
        print("🌿 Using DEVELOP environment")
        print(f"   Supabase Project: yipgbsutqcpiiocsdtay.supabase.co")
    else:
        print("🌳 Using MAIN/PRODUCTION environment")
        print(f"   Supabase Project: lsixcrtzawcxyfkxhyxd.supabase.co")
    
    return True


def main():
    """Run all configuration tests."""
    print("🧪 Testing Branch-Aware Configuration")
    print("=" * 50)
    
    tests = [
        test_branch_detection,
        test_supabase_config,
        test_config_validation,
        test_environment_info
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed with exception: {e}")
            failed += 1
        print()
    
    print("📊 Test Results")
    print("=" * 20)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All configuration tests passed!")
        print("✅ Branch-aware configuration working correctly")
        
        # Show final configuration summary
        print("\n📋 Active Configuration:")
        print(f"   Branch: {config.get_current_branch()}")
        print(f"   Supabase: {'✅ Configured' if config.SUPABASE_URL else '❌ Missing'}")
        print(f"   Voyage AI: {'✅ Configured' if config.VOYAGE_API_KEY else '❌ Missing'}")
        print(f"   Pinecone: {'✅ Configured' if config.PINECONE_API_KEY else '❌ Missing'}")
        print(f"   Neo4j: {'✅ Configured' if config.NEO4J_PASSWORD != 'password' else '⚠️  Default'}")
    else:
        print(f"\n⚠️  {failed} test(s) failed")
        print("🔧 Please check your .env configuration")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
