#!/usr/bin/env python3
"""
Test Supabase connection and registry functionality.

Verifies that we can connect to Supabase and perform basic operations.
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from common.config import config
from common.registry import GlobalRegistry, RegistryEntry


def test_supabase_connection():
    """Test basic Supabase connection."""
    print("🔍 Testing Supabase Connection...")
    
    try:
        registry = GlobalRegistry()
        print(f"✅ Registry client created successfully")
        print(f"   URL: {registry.supabase_url}")
        print(f"   Table: {registry.table_name}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create registry client: {e}")
        return False


def test_registry_operations():
    """Test basic registry operations."""
    print("🔍 Testing Registry Operations...")
    
    try:
        registry = GlobalRegistry()
        
        # Test listing entries (should work even if table is empty)
        entries = registry.list_entries(limit=5)
        print(f"✅ Successfully queried registry")
        print(f"   Found {len(entries)} existing entries")
        
        # Test getting stats
        stats = registry.get_consistency_stats()
        print(f"✅ Successfully retrieved consistency stats")
        print(f"   Stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Registry operations failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        
        # Check if it's a table not found error
        if "relation" in str(e).lower() and "does not exist" in str(e).lower():
            print("💡 This likely means the registry table hasn't been created yet")
            print("   Run the migration: supabase/migrations/20250126_create_registry.sql")
        
        return False


def test_environment_detection():
    """Test that we're using the correct environment."""
    print("🔍 Testing Environment Detection...")
    
    current_branch = config.get_current_branch()
    print(f"Current branch: {current_branch}")
    
    if current_branch == "develop":
        expected_project = "yipgbsutqcpiiocsdtay"
        if expected_project in config.SUPABASE_URL:
            print("✅ Using correct DEVELOP Supabase project")
            return True
        else:
            print("❌ Wrong Supabase project for develop branch")
            return False
    else:
        expected_project = "lsixcrtzawcxyfkxhyxd"
        if expected_project in config.SUPABASE_URL:
            print("✅ Using correct MAIN Supabase project")
            return True
        else:
            print("❌ Wrong Supabase project for main branch")
            return False


def show_migration_instructions():
    """Show instructions for setting up the database."""
    print("\n📋 Database Setup Instructions")
    print("=" * 40)
    
    current_branch = config.get_current_branch()
    if current_branch == "develop":
        project_url = "https://yipgbsutqcpiiocsdtay.supabase.co"
    else:
        project_url = "https://lsixcrtzawcxyfkxhyxd.supabase.co"
    
    print(f"1. Open Supabase dashboard: {project_url}")
    print("2. Go to SQL Editor")
    print("3. Run the migration script:")
    print("   📁 supabase/migrations/20250126_create_registry.sql")
    print("4. Verify the table was created:")
    print("   SELECT * FROM global_registry LIMIT 1;")
    print("5. Re-run this test to verify connection")


def main():
    """Run all connection tests."""
    print("🧪 Testing Supabase Connection & Registry")
    print("=" * 50)
    
    tests = [
        test_environment_detection,
        test_supabase_connection,
        test_registry_operations
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed with exception: {e}")
            failed += 1
        print()
    
    print("📊 Test Results")
    print("=" * 20)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All connection tests passed!")
        print("✅ Supabase registry is ready for use")
    else:
        print(f"\n⚠️  {failed} test(s) failed")
        if failed == 1 and passed >= 2:
            print("💡 Likely just need to run the database migration")
            show_migration_instructions()
        else:
            print("🔧 Please check your Supabase configuration")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
