#!/usr/bin/env python3
"""
Simple Neo4j connection test for ailex-be-ingest.
"""

import sys
import time
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from common.config import config, setup_logging
from common.neo4j import Neo4jClient

def test_neo4j_connection():
    """Test basic Neo4j connection with detailed error handling."""
    print("🔍 Testing Neo4j Connection...")
    print(f"   URI: {config.NEO4J_URI}")
    print(f"   Username: {config.NEO4J_USERNAME}")
    print(f"   Password: {'*' * len(config.NEO4J_PASSWORD)}")
    print()

    # First, test with direct neo4j driver
    print("📡 Testing direct Neo4j driver connection...")
    try:
        from neo4j import GraphDatabase

        driver = GraphDatabase.driver(
            config.NEO4J_URI,
            auth=(config.NEO4J_USERNAME, config.NEO4J_PASSWORD)
        )

        # Test connection
        with driver.session() as session:
            result = session.run("RETURN 1 as test")
            record = result.single()
            if record["test"] == 1:
                print("✅ Direct driver connection successful")

                # Get basic info
                result = session.run("CALL dbms.components() YIELD name, versions, edition")
                for record in result:
                    if record["name"] == "Neo4j Kernel":
                        version = record["versions"][0]
                        edition = record["edition"]
                        print(f"   Neo4j version: {version} ({edition})")
                        break

                # Test database stats
                result = session.run("MATCH (n) RETURN count(n) as node_count")
                node_count = result.single()["node_count"]
                print(f"   Total nodes: {node_count}")

                driver.close()
                print("✅ Direct connection test passed")

                # Now test with Neo4jClient
                print("\n📡 Testing Neo4jClient wrapper...")
                with Neo4jClient() as client:
                    print("✅ Neo4jClient created successfully")

                    with client.driver.session() as session:
                        result = session.run("RETURN 'Hello Neo4j!' as message")
                        message = result.single()["message"]
                        print(f"✅ Neo4jClient query successful: {message}")

                print("\n✅ All Neo4j tests passed successfully!")
                return True
            else:
                print("❌ Unexpected response from Neo4j")
                driver.close()
                return False

    except Exception as e:
        print(f"\n❌ Neo4j connection failed: {e}")
        print(f"   Error type: {type(e).__name__}")

        # Provide troubleshooting hints
        print("\n🔧 Troubleshooting hints:")
        print("   1. Check if Neo4j credentials are correct")
        print("   2. Verify Neo4j server is running and accessible")
        print("   3. Check network connectivity")
        print("   4. Wait a few minutes if you see authentication rate limit errors")
        print("   5. Try logging into Neo4j Browser to verify credentials")

        return False

def main():
    """Main test function."""
    setup_logging()
    
    print("🚀 Neo4j Connection Test")
    print("=" * 40)
    
    # Wait a bit to avoid rate limiting
    print("⏳ Waiting 10 seconds to avoid rate limiting...")
    time.sleep(10)
    
    success = test_neo4j_connection()
    
    if success:
        print("\n🎉 Neo4j is ready for use!")
    else:
        print("\n⚠️  Neo4j connection issues detected")
        
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
