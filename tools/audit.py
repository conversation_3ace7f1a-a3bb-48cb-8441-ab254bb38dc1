#!/usr/bin/env python3
"""
Consistency auditor for ailex-be-ingest.

Verifies that every document in the global registry exists in all four stores:
1. Registry (Supabase)
2. GCS (raw files)
3. Neo4j (graph nodes)
4. <PERSON><PERSON><PERSON> (vectors)

Generates CSV reports and exits with non-zero code if inconsistencies found.
"""

import sys
import csv
import logging
from datetime import datetime, date
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from common.config import config, setup_logging
from common.registry import GlobalRegistry, RegistryEntry, ConsistencyStatus
from common.neo4j import Neo4jClient
from common.models import PINECONE_NAMESPACES

setup_logging()
logger = logging.getLogger(__name__)


class AuditStatus(Enum):
    """Audit status values."""
    OK = "OK"
    MISSING_GCS = "MISSING_GCS"
    MISSING_NODE = "MISSING_NODE"
    VEC_MISMATCH = "VEC_MISMATCH"
    CHECKSUM_MISMATCH = "CHECKSUM_MISMATCH"
    REGISTRY_ERROR = "REGISTRY_ERROR"
    UNKNOWN_ERROR = "UNKNOWN_ERROR"


@dataclass
class AuditResult:
    """Result of auditing a single document."""
    registry_id: str
    source: str
    doc_id: str
    gcs_uri: Optional[str]
    sha256: Optional[str]
    neo4j_loaded: bool
    pinecone_loaded: bool
    article_count: int
    audit_status: AuditStatus
    error_message: Optional[str] = None
    gcs_exists: Optional[bool] = None
    gcs_checksum_match: Optional[bool] = None
    neo4j_exists: Optional[bool] = None
    pinecone_vector_count: Optional[int] = None


class ConsistencyAuditor:
    """Audits consistency across all four stores."""
    
    def __init__(self):
        """Initialize auditor with all required clients."""
        self.registry = GlobalRegistry()
        self.neo4j_client = Neo4jClient()
        
        # Initialize Pinecone
        try:
            from pinecone import Pinecone
            pc = Pinecone(api_key=config.PINECONE_API_KEY)
            self.pinecone_index = pc.Index(config.PINECONE_INDEX)
        except Exception as e:
            logger.error(f"Failed to initialize Pinecone: {e}")
            self.pinecone_index = None
        
        # Initialize GCS
        try:
            from google.cloud import storage
            self.gcs_client = storage.Client()
        except Exception as e:
            logger.error(f"Failed to initialize GCS: {e}")
            self.gcs_client = None
    
    def audit_entries(self, 
                     source: Optional[str] = None,
                     since: Optional[date] = None,
                     limit: int = 1000) -> List[AuditResult]:
        """
        Audit registry entries for consistency.
        
        Args:
            source: Filter by source system
            since: Filter by created_at >= since
            limit: Maximum number of entries to audit
            
        Returns:
            List of AuditResult objects
        """
        logger.info(f"Starting consistency audit (source={source}, since={since}, limit={limit})")
        
        # Get registry entries
        since_dt = datetime.combine(since, datetime.min.time()) if since else None
        entries = self.registry.list_entries(source=source, since=since_dt, limit=limit)
        
        if not entries:
            logger.info("No registry entries found to audit")
            return []
        
        logger.info(f"Auditing {len(entries)} registry entries...")
        
        results = []
        for i, entry in enumerate(entries):
            if i % 10 == 0:
                logger.info(f"Auditing entry {i+1}/{len(entries)}: {entry.source}:{entry.doc_id}")
            
            result = self._audit_single_entry(entry)
            results.append(result)
        
        return results
    
    def _audit_single_entry(self, entry: RegistryEntry) -> AuditResult:
        """Audit a single registry entry."""
        result = AuditResult(
            registry_id=entry.id or "",
            source=entry.source,
            doc_id=entry.doc_id,
            gcs_uri=entry.gcs_uri,
            sha256=entry.sha256,
            neo4j_loaded=entry.neo4j_loaded,
            pinecone_loaded=entry.pinecone_loaded,
            article_count=entry.article_count,
            audit_status=AuditStatus.OK
        )
        
        try:
            # Check GCS
            if entry.gcs_uri:
                gcs_ok, gcs_checksum_match = self._check_gcs(entry.gcs_uri, entry.sha256)
                result.gcs_exists = gcs_ok
                result.gcs_checksum_match = gcs_checksum_match
                
                if not gcs_ok:
                    result.audit_status = AuditStatus.MISSING_GCS
                    result.error_message = f"GCS object not found: {entry.gcs_uri}"
                    return result
                
                if not gcs_checksum_match:
                    result.audit_status = AuditStatus.CHECKSUM_MISMATCH
                    result.error_message = f"GCS checksum mismatch for {entry.gcs_uri}"
                    return result
            
            # Check Neo4j
            if entry.neo4j_loaded:
                neo4j_exists = self._check_neo4j(entry.doc_id)
                result.neo4j_exists = neo4j_exists
                
                if not neo4j_exists:
                    result.audit_status = AuditStatus.MISSING_NODE
                    result.error_message = f"Neo4j node not found: {entry.doc_id}"
                    return result
            
            # Check Pinecone
            if entry.pinecone_loaded:
                vector_count = self._check_pinecone(entry.source, entry.doc_id)
                result.pinecone_vector_count = vector_count
                
                if vector_count != entry.article_count:
                    result.audit_status = AuditStatus.VEC_MISMATCH
                    result.error_message = f"Vector count mismatch: expected {entry.article_count}, found {vector_count}"
                    return result
            
            # All checks passed
            result.audit_status = AuditStatus.OK
            
        except Exception as e:
            logger.error(f"Audit error for {entry.source}:{entry.doc_id}: {e}")
            result.audit_status = AuditStatus.UNKNOWN_ERROR
            result.error_message = str(e)
        
        return result
    
    def _check_gcs(self, gcs_uri: str, expected_sha256: Optional[str]) -> tuple[bool, bool]:
        """Check if GCS object exists and checksum matches."""
        if not self.gcs_client:
            return False, False
        
        try:
            # Parse GCS URI: gs://bucket/path
            if not gcs_uri.startswith("gs://"):
                return False, False
            
            uri_parts = gcs_uri[5:].split("/", 1)
            if len(uri_parts) != 2:
                return False, False
            
            bucket_name, blob_path = uri_parts
            bucket = self.gcs_client.bucket(bucket_name)
            blob = bucket.blob(blob_path)
            
            # Check existence
            if not blob.exists():
                return False, False
            
            # Check checksum if provided
            if expected_sha256:
                # Download and calculate checksum
                content = blob.download_as_text()
                from common.registry import calculate_sha256
                actual_sha256 = calculate_sha256(content)
                return True, actual_sha256 == expected_sha256
            
            return True, True
            
        except Exception as e:
            logger.debug(f"GCS check failed for {gcs_uri}: {e}")
            return False, False
    
    def _check_neo4j(self, doc_id: str) -> bool:
        """Check if Neo4j node exists."""
        try:
            with self.neo4j_client.driver.session() as session:
                result = session.run(
                    "MATCH (a:Act {id: $id}) RETURN count(a) = 1 as exists",
                    {"id": doc_id}
                )
                record = result.single()
                return record["exists"] if record else False
                
        except Exception as e:
            logger.debug(f"Neo4j check failed for {doc_id}: {e}")
            return False
    
    def _check_pinecone(self, source: str, doc_id: str) -> int:
        """Check vector count in Pinecone for a document."""
        if not self.pinecone_index:
            return 0
        
        try:
            namespace = PINECONE_NAMESPACES.get(source, source)
            
            # Query vectors with metadata filter
            query_result = self.pinecone_index.query(
                vector=[0.0] * config.VOYAGE_DIMENSION,  # Dummy vector
                top_k=10000,  # Large number to get all
                namespace=namespace,
                filter={"act_id": doc_id},
                include_metadata=True
            )
            
            return len(query_result.matches)
            
        except Exception as e:
            logger.debug(f"Pinecone check failed for {source}:{doc_id}: {e}")
            return 0
    
    def generate_report(self, results: List[AuditResult], output_file: Optional[str] = None) -> Dict[str, int]:
        """
        Generate CSV report and return summary statistics.
        
        Args:
            results: List of audit results
            output_file: Optional output file path
            
        Returns:
            Dictionary with status counts
        """
        if not results:
            logger.info("No results to report")
            return {}
        
        # Calculate statistics
        stats = {}
        for result in results:
            status = result.audit_status.value
            stats[status] = stats.get(status, 0) + 1
        
        # Generate CSV report
        if output_file:
            output_path = Path(output_file)
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = Path(f"audit_report_{timestamp}.csv")

        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', newline='') as csvfile:
            fieldnames = [
                'registry_id', 'source', 'doc_id', 'gcs_uri', 'sha256',
                'neo4j_loaded', 'pinecone_loaded', 'article_count',
                'audit_status', 'error_message', 'gcs_exists', 'gcs_checksum_match',
                'neo4j_exists', 'pinecone_vector_count'
            ]
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in results:
                row = asdict(result)
                # Convert enum to string
                row['audit_status'] = result.audit_status.value
                writer.writerow(row)
        
        logger.info(f"Audit report written to: {output_path}")
        
        # Print summary
        print("\n📊 Audit Summary")
        print("=" * 30)
        total = len(results)
        for status, count in sorted(stats.items()):
            percentage = (count / total) * 100
            print(f"{status:20} {count:6} ({percentage:5.1f}%)")
        print(f"{'TOTAL':20} {total:6}")
        
        return stats
    
    def close(self):
        """Clean up resources."""
        if hasattr(self, 'neo4j_client'):
            self.neo4j_client.close()


def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Audit consistency across all stores")
    parser.add_argument("--source", choices=["vlaamse", "eu_dump", "eu_rest"], help="Filter by source")
    parser.add_argument("--since", type=str, help="Filter by date (YYYY-MM-DD)")
    parser.add_argument("--limit", type=int, default=1000, help="Maximum entries to audit")
    parser.add_argument("--output", type=str, help="Output CSV file path")
    parser.add_argument("--fail-on-errors", action="store_true", help="Exit with non-zero code if errors found")
    
    args = parser.parse_args()
    
    # Parse date
    since_date = None
    if args.since:
        try:
            since_date = datetime.strptime(args.since, '%Y-%m-%d').date()
        except ValueError:
            print(f"❌ Invalid date format: {args.since}. Use YYYY-MM-DD")
            sys.exit(1)
    
    print("🔍 Starting Consistency Audit")
    print("=" * 40)
    
    try:
        auditor = ConsistencyAuditor()
        
        # Run audit
        results = auditor.audit_entries(
            source=args.source,
            since=since_date,
            limit=args.limit
        )
        
        if not results:
            print("✅ No entries found to audit")
            return
        
        # Generate report
        stats = auditor.generate_report(results, args.output)
        
        # Check for errors
        error_count = sum(count for status, count in stats.items() if status != "OK")
        
        if error_count == 0:
            print("\n✅ All entries are consistent!")
            exit_code = 0
        else:
            print(f"\n⚠️  Found {error_count} inconsistencies")
            if args.fail_on_errors:
                exit_code = 1
            else:
                exit_code = 0
        
        auditor.close()
        sys.exit(exit_code)
        
    except Exception as e:
        print(f"❌ Audit failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
