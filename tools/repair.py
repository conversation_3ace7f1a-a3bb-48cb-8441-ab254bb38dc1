#!/usr/bin/env python3
"""
Repair tool for ailex-be-ingest.

Fixes inconsistencies found by the audit tool by reloading data from
authoritative sources (GCS raw files).
"""

import sys
import logging
from datetime import datetime, date
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from common.config import config, setup_logging
from common.registry import GlobalRegistry, RegistryEntry
from common.transaction import TransactionalIngester
from common.models import CommonAct, CommonArticle

setup_logging()
logger = logging.getLogger(__name__)


@dataclass
class RepairResult:
    """Result of repairing a single document."""
    source: str
    doc_id: str
    success: bool
    error_message: Optional[str] = None
    neo4j_repaired: bool = False
    pinecone_repaired: bool = False


class RepairTool:
    """Repairs inconsistencies by reloading from GCS."""
    
    def __init__(self, dry_run: bool = False):
        """
        Initialize repair tool.
        
        Args:
            dry_run: If True, don't actually make changes
        """
        self.dry_run = dry_run
        self.registry = GlobalRegistry()
        
        # Initialize GCS client
        try:
            from google.cloud import storage
            self.gcs_client = storage.Client()
        except Exception as e:
            logger.error(f"Failed to initialize GCS: {e}")
            self.gcs_client = None
        
        # Initialize parsers for each source
        self.parsers = {}
        self._setup_parsers()
    
    def _setup_parsers(self):
        """Setup parsers for each source type."""
        try:
            from sources.vlaamse_codex.parse import VlaamseCodexParser
            self.parsers['vlaamse'] = VlaamseCodexParser()
        except ImportError:
            logger.warning("Vlaamse Codex parser not available")
        
        # TODO: Add EUR-Lex parsers when implemented
        # from sources.eurlex.parse import EurlexParser
        # self.parsers['eu_dump'] = EurlexParser()
        # self.parsers['eu_rest'] = EurlexParser()
    
    def repair_missing_neo4j(self, 
                           source: Optional[str] = None,
                           since: Optional[date] = None,
                           limit: int = 100) -> List[RepairResult]:
        """
        Repair entries where neo4j_loaded=false by reloading from GCS.
        
        Args:
            source: Filter by source system
            since: Filter by created_at >= since
            limit: Maximum entries to repair
            
        Returns:
            List of RepairResult objects
        """
        logger.info(f"Repairing missing Neo4j entries (source={source}, since={since}, limit={limit})")
        
        # Get entries with neo4j_loaded=false
        entries = self._get_entries_needing_repair(source, since, limit, neo4j_loaded=False)
        
        if not entries:
            logger.info("No entries found needing Neo4j repair")
            return []
        
        logger.info(f"Found {len(entries)} entries needing Neo4j repair")
        
        results = []
        for entry in entries:
            result = self._repair_single_entry(entry, repair_neo4j=True, repair_pinecone=False)
            results.append(result)
        
        return results
    
    def repair_vec_mismatch(self,
                          source: Optional[str] = None,
                          since: Optional[date] = None,
                          limit: int = 100) -> List[RepairResult]:
        """
        Repair vector mismatches by re-chunking and re-upserting to Pinecone.
        
        Args:
            source: Filter by source system
            since: Filter by created_at >= since
            limit: Maximum entries to repair
            
        Returns:
            List of RepairResult objects
        """
        logger.info(f"Repairing vector mismatches (source={source}, since={since}, limit={limit})")
        
        # Get entries with pinecone_loaded=false or potential mismatches
        entries = self._get_entries_needing_repair(source, since, limit, pinecone_loaded=False)
        
        if not entries:
            logger.info("No entries found needing vector repair")
            return []
        
        logger.info(f"Found {len(entries)} entries needing vector repair")
        
        results = []
        for entry in entries:
            result = self._repair_single_entry(entry, repair_neo4j=False, repair_pinecone=True)
            results.append(result)
        
        return results
    
    def repair_all_inconsistencies(self,
                                 source: Optional[str] = None,
                                 since: Optional[date] = None,
                                 limit: int = 100) -> List[RepairResult]:
        """
        Repair all inconsistencies by reloading everything from GCS.
        
        Args:
            source: Filter by source system
            since: Filter by created_at >= since
            limit: Maximum entries to repair
            
        Returns:
            List of RepairResult objects
        """
        logger.info(f"Repairing all inconsistencies (source={source}, since={since}, limit={limit})")
        
        # Get all inconsistent entries
        inconsistent_entries = self.registry.get_inconsistent_entries(source, since)
        
        if limit:
            inconsistent_entries = inconsistent_entries[:limit]
        
        if not inconsistent_entries:
            logger.info("No inconsistent entries found")
            return []
        
        logger.info(f"Found {len(inconsistent_entries)} inconsistent entries")
        
        results = []
        for entry in inconsistent_entries:
            result = self._repair_single_entry(entry, repair_neo4j=True, repair_pinecone=True)
            results.append(result)
        
        return results
    
    def _get_entries_needing_repair(self,
                                  source: Optional[str],
                                  since: Optional[date],
                                  limit: int,
                                  neo4j_loaded: Optional[bool] = None,
                                  pinecone_loaded: Optional[bool] = None) -> List[RegistryEntry]:
        """Get registry entries that need repair."""
        since_dt = datetime.combine(since, datetime.min.time()) if since else None
        all_entries = self.registry.list_entries(source=source, since=since_dt, limit=limit * 2)  # Get more to filter
        
        filtered_entries = []
        for entry in all_entries:
            if neo4j_loaded is not None and entry.neo4j_loaded != neo4j_loaded:
                continue
            if pinecone_loaded is not None and entry.pinecone_loaded != pinecone_loaded:
                continue
            filtered_entries.append(entry)
            
            if len(filtered_entries) >= limit:
                break
        
        return filtered_entries
    
    def _repair_single_entry(self, 
                           entry: RegistryEntry,
                           repair_neo4j: bool = True,
                           repair_pinecone: bool = True) -> RepairResult:
        """Repair a single registry entry."""
        logger.info(f"Repairing {entry.source}:{entry.doc_id}")
        
        result = RepairResult(
            source=entry.source,
            doc_id=entry.doc_id,
            success=False
        )
        
        try:
            # Download raw content from GCS
            if not entry.gcs_uri:
                result.error_message = "No GCS URI in registry entry"
                return result
            
            raw_content = self._download_from_gcs(entry.gcs_uri)
            if not raw_content:
                result.error_message = f"Failed to download from GCS: {entry.gcs_uri}"
                return result
            
            # Parse content
            parser = self.parsers.get(entry.source)
            if not parser:
                result.error_message = f"No parser available for source: {entry.source}"
                return result
            
            # For Vlaamse Codex, we need metadata - reconstruct basic metadata
            metadata = {
                "Id": entry.doc_id,
                "DocumentUrlXML": entry.gcs_uri
            }
            
            try:
                act, articles = parser.parse_document(raw_content, metadata)
            except Exception as e:
                result.error_message = f"Failed to parse document: {e}"
                return result
            
            if self.dry_run:
                logger.info(f"DRY RUN: Would repair {entry.source}:{entry.doc_id}")
                result.success = True
                result.neo4j_repaired = repair_neo4j
                result.pinecone_repaired = repair_pinecone
                return result
            
            # Use transactional ingester to repair
            ingester = TransactionalIngester(
                source=entry.source,
                dry_run=False,
                save_raw_files=False,  # Don't re-save to GCS
                use_registry=False  # Don't create new registry entry
            )
            
            # Manually repair specific components
            if repair_neo4j:
                try:
                    with ingester.neo4j_client.driver.session() as session:
                        with session.begin_transaction() as tx:
                            ingester._upsert_act_tx(tx, act)
                            for article in articles:
                                ingester._upsert_article_tx(tx, article)
                            tx.commit()
                    
                    # Update registry flag
                    self.registry.update_flags(entry.source, entry.doc_id, neo4j_loaded=True)
                    result.neo4j_repaired = True
                    logger.info(f"Repaired Neo4j for {entry.source}:{entry.doc_id}")
                    
                except Exception as e:
                    logger.error(f"Failed to repair Neo4j for {entry.source}:{entry.doc_id}: {e}")
                    result.error_message = f"Neo4j repair failed: {e}"
                    return result
            
            if repair_pinecone:
                try:
                    vectors_count = ingester._create_and_upsert_vectors(act, articles)
                    
                    # Update registry flag
                    self.registry.update_flags(
                        entry.source, 
                        entry.doc_id, 
                        pinecone_loaded=True,
                        article_count=vectors_count
                    )
                    result.pinecone_repaired = True
                    logger.info(f"Repaired Pinecone for {entry.source}:{entry.doc_id} ({vectors_count} vectors)")
                    
                except Exception as e:
                    logger.error(f"Failed to repair Pinecone for {entry.source}:{entry.doc_id}: {e}")
                    result.error_message = f"Pinecone repair failed: {e}"
                    return result
            
            ingester.close()
            result.success = True
            
        except Exception as e:
            logger.error(f"Repair failed for {entry.source}:{entry.doc_id}: {e}")
            result.error_message = str(e)
        
        return result
    
    def _download_from_gcs(self, gcs_uri: str) -> Optional[str]:
        """Download content from GCS."""
        if not self.gcs_client:
            return None
        
        try:
            # Parse GCS URI: gs://bucket/path
            if not gcs_uri.startswith("gs://"):
                return None
            
            uri_parts = gcs_uri[5:].split("/", 1)
            if len(uri_parts) != 2:
                return None
            
            bucket_name, blob_path = uri_parts
            bucket = self.gcs_client.bucket(bucket_name)
            blob = bucket.blob(blob_path)
            
            return blob.download_as_text()
            
        except Exception as e:
            logger.error(f"Failed to download from GCS {gcs_uri}: {e}")
            return None


def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Repair inconsistencies found by audit")
    parser.add_argument("--missing-neo4j", action="store_true", help="Repair missing Neo4j nodes")
    parser.add_argument("--vec-mismatch", action="store_true", help="Repair vector mismatches")
    parser.add_argument("--all", action="store_true", help="Repair all inconsistencies")
    parser.add_argument("--source", choices=["vlaamse", "eu_dump", "eu_rest"], help="Filter by source")
    parser.add_argument("--since", type=str, help="Filter by date (YYYY-MM-DD)")
    parser.add_argument("--limit", type=int, default=100, help="Maximum entries to repair")
    parser.add_argument("--dry-run", action="store_true", help="Don't actually make changes")
    
    args = parser.parse_args()
    
    if not any([args.missing_neo4j, args.vec_mismatch, args.all]):
        print("❌ Must specify one of: --missing-neo4j, --vec-mismatch, --all")
        sys.exit(1)
    
    # Parse date
    since_date = None
    if args.since:
        try:
            since_date = datetime.strptime(args.since, '%Y-%m-%d').date()
        except ValueError:
            print(f"❌ Invalid date format: {args.since}. Use YYYY-MM-DD")
            sys.exit(1)
    
    print("🔧 Starting Repair Process")
    print("=" * 30)
    
    try:
        repair_tool = RepairTool(dry_run=args.dry_run)
        
        results = []
        
        if args.missing_neo4j:
            results.extend(repair_tool.repair_missing_neo4j(args.source, since_date, args.limit))
        
        if args.vec_mismatch:
            results.extend(repair_tool.repair_vec_mismatch(args.source, since_date, args.limit))
        
        if args.all:
            results.extend(repair_tool.repair_all_inconsistencies(args.source, since_date, args.limit))
        
        # Print summary
        if results:
            successful = sum(1 for r in results if r.success)
            failed = len(results) - successful
            neo4j_repaired = sum(1 for r in results if r.neo4j_repaired)
            pinecone_repaired = sum(1 for r in results if r.pinecone_repaired)
            
            print(f"\n📊 Repair Summary")
            print(f"Total processed: {len(results)}")
            print(f"Successful: {successful}")
            print(f"Failed: {failed}")
            print(f"Neo4j repaired: {neo4j_repaired}")
            print(f"Pinecone repaired: {pinecone_repaired}")
            
            if failed > 0:
                print(f"\n❌ {failed} repairs failed")
                for result in results:
                    if not result.success:
                        print(f"  {result.source}:{result.doc_id} - {result.error_message}")
            else:
                print(f"\n✅ All repairs completed successfully!")
        else:
            print("✅ No entries found needing repair")
        
    except Exception as e:
        print(f"❌ Repair failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
