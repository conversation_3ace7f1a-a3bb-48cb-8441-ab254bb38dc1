#!/usr/bin/env python3
"""
Test the HF pipeline with sample Belgian State Gazette data
This bypasses the HF rate limiting and demonstrates the full pipeline
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Add the etl/moniteur directory to the path
sys.path.append(str(Path(__file__).parent / "etl" / "moniteur"))

from pull_from_hf import chunk_text, embed_batch, setup_pinecone

load_dotenv()

def test_full_pipeline():
    """Test the complete pipeline with sample Belgian State Gazette data"""
    print("🧪 Testing Complete HF Pipeline with Sample Data")
    print("=" * 60)
    
    # Sample Belgian State Gazette records (similar to HF dataset structure)
    sample_records = [
        {
            "year": 2024,
            "month": 7,
            "day": 25,
            "lang": "nl",
            "uri": "https://www.ejustice.just.fgov.be/eli/besluit/2024/07/25/test1/nl",
            "text": """
            KONINKLIJK BESLUIT van 25 juli 2024 betreffende de wijziging van de 
            regelgeving inzake vennootschappen. Dit besluit wijzigt verschillende 
            bepalingen van het Wetboek van vennootschappen en verenigingen. 
            Artikel 1: De bepalingen van hoofdstuk 3 worden als volgt gewijzigd.
            Artikel 2: Dit besluit treedt in werking op 1 januari 2025.
            """
        },
        {
            "year": 2024,
            "month": 7,
            "day": 25,
            "lang": "fr", 
            "uri": "https://www.ejustice.just.fgov.be/eli/arrete/2024/07/25/test2/fr",
            "text": """
            ARRÊTÉ ROYAL du 25 juillet 2024 modifiant la réglementation relative 
            aux sociétés. Cet arrêté modifie diverses dispositions du Code des 
            sociétés et des associations. Article 1er: Les dispositions du 
            chapitre 3 sont modifiées comme suit. Article 2: Le présent arrêté 
            entre en vigueur le 1er janvier 2025.
            """
        },
        {
            "year": 2024,
            "month": 7,
            "day": 24,
            "lang": "nl",
            "uri": "https://www.ejustice.just.fgov.be/eli/wet/2024/07/24/test3/nl",
            "text": """
            WET van 24 juli 2024 tot wijziging van de wet betreffende de 
            handelsregister. Deze wet voert belangrijke wijzigingen door in 
            de procedures voor de registratie van ondernemingen. Hoofdstuk I 
            behandelt de algemene bepalingen. Hoofdstuk II regelt de 
            inschrijvingsprocedures. Hoofdstuk III bevat de overgangsbepalingen.
            """
        }
    ]
    
    print(f"📄 Processing {len(sample_records)} sample records...")
    
    # Setup Pinecone
    try:
        index = setup_pinecone()
        print("✅ Pinecone connection established")
    except Exception as e:
        print(f"❌ Pinecone setup failed: {e}")
        return False
    
    total_chunks = 0
    total_vectors = 0
    
    # Process each record
    for i, record in enumerate(sample_records, 1):
        print(f"\n🔄 Processing record {i}/{len(sample_records)}")
        print(f"   📅 Date: {record['year']}-{record['month']:02d}-{record['day']:02d}")
        print(f"   🌐 Language: {record['lang']}")
        print(f"   📝 Text length: {len(record['text'])} characters")
        
        # Extract metadata
        meta = {
            "year": record["year"],
            "month": record["month"], 
            "day": record["day"],
            "lang": record["lang"],
            "source": "Moniteur Belge",
            "eli": record["uri"]
        }
        
        # Create chunks
        chunks = list(chunk_text(record["text"]))
        print(f"   📝 Created {len(chunks)} chunks")
        total_chunks += len(chunks)
        
        if not chunks:
            print("   ⚠️ No chunks created, skipping...")
            continue
        
        # Create embeddings
        try:
            embeddings = embed_batch(chunks)
            print(f"   🔮 Generated {len(embeddings)} embeddings")
        except Exception as e:
            print(f"   ❌ Embedding failed: {e}")
            continue
        
        # Prepare vectors for Pinecone
        vectors_to_upsert = []
        for chunk_content, embedding in zip(chunks, embeddings):
            import hashlib
            vector_id = hashlib.md5(chunk_content.encode()).hexdigest()
            vectors_to_upsert.append({
                "id": vector_id,
                "values": embedding,
                "metadata": {
                    **meta,
                    "text": chunk_content[:500]  # Store preview of text
                }
            })
        
        # Upload to Pinecone
        try:
            index.upsert(vectors=vectors_to_upsert, namespace="moniteur")
            print(f"   ✅ Uploaded {len(vectors_to_upsert)} vectors to Pinecone")
            total_vectors += len(vectors_to_upsert)
        except Exception as e:
            print(f"   ❌ Pinecone upload failed: {e}")
            continue
    
    print(f"\n🎉 Pipeline Test Complete!")
    print(f"📊 Summary:")
    print(f"   Records processed: {len(sample_records)}")
    print(f"   Total chunks created: {total_chunks}")
    print(f"   Total vectors uploaded: {total_vectors}")
    print(f"   Average chunks per record: {total_chunks / len(sample_records):.1f}")
    
    # Test a simple query to verify the data is searchable
    print(f"\n🔍 Testing search functionality...")
    try:
        query_results = index.query(
            vector=[0.1] * 1024,  # Dummy query vector
            top_k=3,
            namespace="moniteur",
            include_metadata=True
        )
        print(f"✅ Search test successful! Found {len(query_results.matches)} results")
        for match in query_results.matches[:2]:
            preview = match.metadata.get('text', '')[:100]
            print(f"   📄 {match.metadata.get('lang', 'unknown')}: {preview}...")
    except Exception as e:
        print(f"❌ Search test failed: {e}")
    
    return True

if __name__ == "__main__":
    success = test_full_pipeline()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}: Pipeline test completed")
    sys.exit(0 if success else 1)
