"""
Unit tests for Vlaamse Codex ingestion components.
"""

import pytest
from datetime import date
from unittest.mock import Mock, patch, MagicMock

from sources.vlaamse_codex.client import VlaamseCodexClient
from sources.vlaamse_codex.parse import VlaamseCodexParser
from sources.vlaamse_codex.ingest import VlaamseCodexIngester
from common.models import CommonAct, CommonArticle
from common.neo4j import Neo4jClient


class TestVlaamseCodexClient:
    """Test the Vlaamse Codex API client."""
    
    def test_client_initialization(self):
        """Test client initialization with default config."""
        client = VlaamseCodexClient()
        assert client.base_url is not None
        assert client.rate_limit >= 0
        assert client.session is not None
    
    @patch('requests.Session.get')
    def test_get_documents_success(self, mock_get):
        """Test successful document retrieval."""
        # Mock API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "value": [
                {"Id": "doc1", "Titel": "Test Document 1"},
                {"Id": "doc2", "Titel": "Test Document 2"}
            ]
        }
        mock_get.return_value = mock_response
        
        client = VlaamseCodexClient()
        documents = list(client.get_documents(max_pages=1))
        
        assert len(documents) == 2
        assert documents[0]["Id"] == "doc1"
        assert documents[1]["Id"] == "doc2"
    
    @patch('requests.Session.get')
    def test_get_documents_with_cursor(self, mock_get):
        """Test document retrieval with date cursor."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"value": []}
        mock_get.return_value = mock_response
        
        client = VlaamseCodexClient()
        cursor_date = date(2024, 1, 1)
        list(client.get_documents(cursor_date=cursor_date, max_pages=1))
        
        # Verify the filter parameter was included
        call_args = mock_get.call_args
        params = call_args[1]['params']
        assert '$filter' in params
        assert '2024-01-01' in params['$filter']
    
    @patch('requests.Session.get')
    def test_download_xml_success(self, mock_get):
        """Test successful XML download."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "<xml>test content</xml>"
        mock_response.headers = {"content-type": "application/xml"}
        mock_get.return_value = mock_response
        
        client = VlaamseCodexClient()
        xml_content = client.download_xml("http://example.com/test.xml")
        
        assert xml_content == "<xml>test content</xml>"
    
    @patch('requests.Session.get')
    def test_download_xml_failure(self, mock_get):
        """Test XML download failure."""
        mock_get.side_effect = Exception("Network error")
        
        client = VlaamseCodexClient()
        xml_content = client.download_xml("http://example.com/test.xml")
        
        assert xml_content is None


class TestVlaamseCodexParser:
    """Test the Vlaamse Codex XML parser."""
    
    def test_parse_document_success(self, sample_vlaamse_metadata, sample_vlaamse_xml):
        """Test successful document parsing."""
        parser = VlaamseCodexParser()
        act, articles = parser.parse_document(sample_vlaamse_xml, sample_vlaamse_metadata)
        
        # Verify act
        assert isinstance(act, CommonAct)
        assert act.source == "vlaamse"
        assert act.title == "Koninklijk besluit betreffende testregeling"
        assert act.language == "nl"
        assert act.eli == "eli:be-vlg:kb:2024:07:25:test"
        
        # Verify articles
        assert len(articles) == 2
        assert all(isinstance(art, CommonArticle) for art in articles)
        assert articles[0].number == "1"
        assert articles[0].heading == "Algemene bepalingen"
        assert "algemene bepalingen" in articles[0].text.lower()
        assert articles[1].number == "2"
        assert articles[1].heading == "Specifieke bepalingen"
    
    def test_parse_invalid_xml(self, sample_vlaamse_metadata):
        """Test parsing with invalid XML."""
        parser = VlaamseCodexParser()
        invalid_xml = "<invalid>unclosed tag"
        
        with pytest.raises(Exception):
            parser.parse_document(invalid_xml, sample_vlaamse_metadata)
    
    def test_extract_date_various_formats(self):
        """Test date extraction with various formats."""
        parser = VlaamseCodexParser()
        
        # ISO format with timezone
        metadata1 = {"DatumPublicatie": "2024-07-25T00:00:00Z"}
        date1 = parser._extract_date(metadata1)
        assert date1 == date(2024, 7, 25)
        
        # Simple date format
        metadata2 = {"DatumPublicatie": "2024-07-25"}
        date2 = parser._extract_date(metadata2)
        assert date2 == date(2024, 7, 25)
        
        # No date
        metadata3 = {}
        date3 = parser._extract_date(metadata3)
        assert date3 is None
    
    def test_create_act_id_with_numac(self):
        """Test act ID creation with NUMAC."""
        parser = VlaamseCodexParser()
        metadata = {"NUMAC": "2024070123"}
        act_id = parser._create_act_id(metadata)
        assert act_id == "vlaamse_2024070123"
    
    def test_create_act_id_fallback(self):
        """Test act ID creation without NUMAC."""
        parser = VlaamseCodexParser()
        metadata = {
            "Id": "test_doc_123",
            "Titel": "Test Document",
            "DatumPublicatie": "2024-07-25"
        }
        act_id = parser._create_act_id(metadata)
        assert "vlaamse_test_doc_123" == act_id


class TestVlaamseCodexIngester:
    """Test the complete Vlaamse Codex ingestion pipeline."""
    
    def test_ingester_initialization_dry_run(self):
        """Test ingester initialization in dry run mode."""
        ingester = VlaamseCodexIngester(dry_run=True)
        assert ingester.dry_run is True
        assert not hasattr(ingester, 'voyage_client')  # Services not initialized in dry run
    
    @patch('sources.vlaamse_codex.ingest.VlaamseCodexClient')
    @patch('sources.vlaamse_codex.ingest.VlaamseCodexParser')
    def test_process_document_dry_run(self, mock_parser_class, mock_client_class, 
                                    sample_vlaamse_metadata, sample_common_act, 
                                    sample_common_articles):
        """Test document processing in dry run mode."""
        # Setup mocks
        mock_client = Mock()
        mock_client.download_xml.return_value = "<xml>test</xml>"
        mock_client_class.return_value = mock_client
        
        mock_parser = Mock()
        mock_parser.parse_document.return_value = (sample_common_act, sample_common_articles)
        mock_parser_class.return_value = mock_parser
        
        # Test processing
        ingester = VlaamseCodexIngester(dry_run=True)
        ingester._process_document(sample_vlaamse_metadata)
        
        # Verify calls
        mock_client.download_xml.assert_called_once()
        mock_parser.parse_document.assert_called_once()
        assert ingester.stats.acts_created == 1
        assert ingester.stats.articles_created == 2
    
    def test_chunk_text(self):
        """Test text chunking functionality."""
        ingester = VlaamseCodexIngester(dry_run=True, chunk_size=50, chunk_overlap=10)
        
        # Test with short text
        short_text = "This is a short text."
        chunks = ingester._chunk_text(short_text)
        assert len(chunks) == 0  # Too short to chunk
        
        # Test with longer text
        long_text = " ".join(["This is a longer text that should be chunked."] * 20)
        chunks = ingester._chunk_text(long_text)
        assert len(chunks) > 0
        assert all(isinstance(chunk, str) for chunk in chunks)


class TestNeo4jIntegration:
    """Test Neo4j integration components."""
    
    def test_neo4j_client_initialization(self):
        """Test Neo4j client initialization."""
        with patch('neo4j.GraphDatabase.driver') as mock_driver:
            mock_session = Mock()
            mock_driver.return_value.session.return_value.__enter__.return_value = mock_session
            mock_session.run.return_value = Mock()
            
            client = Neo4jClient()
            assert client.driver is not None
    
    def test_upsert_act(self, sample_common_act):
        """Test act upsert functionality."""
        with patch('neo4j.GraphDatabase.driver') as mock_driver:
            mock_session = Mock()
            mock_driver.return_value.session.return_value.__enter__.return_value = mock_session
            mock_result = Mock()
            mock_result.single.return_value = {"id": sample_common_act.id}
            mock_session.run.return_value = mock_result
            
            client = Neo4jClient()
            success = client.upsert_act(sample_common_act)
            
            assert success is True
            mock_session.run.assert_called()
    
    def test_upsert_article(self, sample_common_articles):
        """Test article upsert functionality."""
        with patch('neo4j.GraphDatabase.driver') as mock_driver:
            mock_session = Mock()
            mock_driver.return_value.session.return_value.__enter__.return_value = mock_session
            mock_result = Mock()
            mock_result.single.return_value = {"id": sample_common_articles[0].id}
            mock_session.run.return_value = mock_result
            
            client = Neo4jClient()
            success = client.upsert_article(sample_common_articles[0])
            
            assert success is True
            mock_session.run.assert_called()


class TestDataModels:
    """Test the unified data models."""
    
    def test_common_act_creation(self):
        """Test CommonAct model creation and validation."""
        act = CommonAct(
            id="test_act_123",
            title="Test Act",
            date=date(2024, 7, 25),
            language="nl",
            source="vlaamse",
            eli="eli:be-vlg:kb:2024:07:25:test"
        )
        
        assert act.id == "test_act_123"
        assert act.source == "vlaamse"
        assert act.language == "nl"
        assert act.metadata == {}  # Default empty dict
    
    def test_common_article_creation(self):
        """Test CommonArticle model creation and validation."""
        article = CommonArticle(
            id="test_act_123#art1",
            act_id="test_act_123",
            number="1",
            text="This is the article text.",
            language="nl"
        )
        
        assert article.id == "test_act_123#art1"
        assert article.act_id == "test_act_123"
        assert article.number == "1"
        assert article.heading is None  # Optional field
        assert article.language == "nl"
    
    def test_common_act_json_serialization(self, sample_common_act):
        """Test JSON serialization of CommonAct."""
        json_data = sample_common_act.model_dump()
        
        assert json_data["id"] == sample_common_act.id
        assert json_data["source"] == "vlaamse"
        assert isinstance(json_data["date"], str)  # Date should be serialized as string
