"""
End-to-end smoke tests for ailex-be-ingest.

These tests verify that the complete pipeline works by:
1. Loading a small set of documents
2. Verifying they are stored in Neo4j
3. Verifying vectors are created in Pinecone
4. Testing basic retrieval functionality
"""

import os
import pytest
import time
from datetime import date, timedelta
from unittest.mock import patch, Mock

from pipelines.initial_load import initial_load_flow
from common.neo4j import Neo4jClient
from common.config import config


@pytest.mark.e2e
@pytest.mark.skipif(
    not all([
        os.getenv("VOYAGE_API_KEY"),
        os.getenv("PINECONE_API_KEY"),
        os.getenv("NEO4J_PASSWORD")
    ]),
    reason="E2E tests require real API keys and Neo4j"
)
class TestE2ESmoke:
    """End-to-end smoke tests."""
    
    def test_complete_pipeline_small_dataset(self):
        """Test complete pipeline with a small dataset."""
        
        # Run pipeline with small limit
        result = initial_load_flow(
            limit=5,  # Small number for testing
            dry_run=False,
            vlaamse_only=True,
            skip_infrastructure=False
        )
        
        # Verify pipeline completed successfully
        assert result["success"] is True
        assert result["total_documents_processed"] >= 1
        
        # Verify data was stored
        self._verify_neo4j_data()
        self._verify_pinecone_data()
    
    def test_dry_run_pipeline(self):
        """Test pipeline in dry run mode."""
        
        result = initial_load_flow(
            limit=10,
            dry_run=True,
            vlaamse_only=True,
            skip_infrastructure=True
        )
        
        # Dry run should complete without errors
        assert result["success"] is True
        assert result["dry_run"] is True
    
    def _verify_neo4j_data(self):
        """Verify data was stored in Neo4j."""
        
        with Neo4jClient() as client:
            # Check that acts were created
            act_stats = client.get_act_stats()
            assert "vlaamse" in act_stats
            assert act_stats["vlaamse"] > 0
            
            # Check that articles were created
            article_stats = client.get_article_stats()
            assert len(article_stats) > 0
            
            # Verify relationships exist
            with client.driver.session() as session:
                result = session.run("""
                    MATCH (a:Act)-[:HAS_ARTICLE]->(art:Article)
                    RETURN count(*) as relationship_count
                """)
                relationship_count = result.single()["relationship_count"]
                assert relationship_count > 0
    
    def _verify_pinecone_data(self):
        """Verify vectors were created in Pinecone."""
        
        from pinecone import Pinecone
        
        pc = Pinecone(api_key=config.PINECONE_API_KEY)
        index = pc.Index(config.PINECONE_INDEX)
        
        # Check index stats
        stats = index.describe_index_stats()
        assert stats.total_vector_count > 0
        
        # Check that vlaamse namespace has vectors
        if "vlaamse_codex" in stats.namespaces:
            vlaamse_stats = stats.namespaces["vlaamse_codex"]
            assert vlaamse_stats.vector_count > 0
    
    def test_search_functionality(self):
        """Test basic search functionality after ingestion."""
        
        from pinecone import Pinecone
        import voyageai
        
        # Create a test query
        query_text = "algemene bepalingen"
        
        # Create embedding for query
        voyage_client = voyageai.Client(api_key=config.VOYAGE_API_KEY)
        query_result = voyage_client.embed(
            texts=[query_text],
            model=config.VOYAGE_MODEL,
            output_dimension=config.VOYAGE_DIMENSION
        )
        query_vector = query_result.embeddings[0]
        
        # Search in Pinecone
        pc = Pinecone(api_key=config.PINECONE_API_KEY)
        index = pc.Index(config.PINECONE_INDEX)
        
        search_results = index.query(
            vector=query_vector,
            top_k=5,
            namespace="vlaamse_codex",
            include_metadata=True
        )
        
        # Verify we got results
        assert len(search_results.matches) > 0
        
        # Verify metadata structure
        for match in search_results.matches:
            assert "act_id" in match.metadata
            assert "article_id" in match.metadata
            assert "language" in match.metadata
            assert "source" in match.metadata


@pytest.mark.integration
class TestIntegrationMocked:
    """Integration tests with mocked external services."""
    
    @patch('sources.vlaamse_codex.client.VlaamseCodexClient')
    @patch('voyageai.Client')
    @patch('pinecone.Pinecone')
    @patch('common.neo4j.Neo4jClient')
    def test_pipeline_with_mocked_services(
        self, 
        mock_neo4j, 
        mock_pinecone, 
        mock_voyage, 
        mock_client
    ):
        """Test pipeline with all external services mocked."""
        
        # Setup mocks
        self._setup_mocks(mock_client, mock_voyage, mock_pinecone, mock_neo4j)
        
        # Run pipeline
        result = initial_load_flow(
            limit=3,
            dry_run=False,
            vlaamse_only=True,
            skip_infrastructure=True
        )
        
        # Verify pipeline completed
        assert result["success"] is True
        assert result["total_documents_processed"] >= 0
    
    def _setup_mocks(self, mock_client_class, mock_voyage_class, mock_pinecone_class, mock_neo4j_class):
        """Setup all mocks for integration testing."""
        
        # Mock Vlaamse Codex client
        mock_client = Mock()
        mock_client.get_documents.return_value = [
            {
                "Id": "test_doc_1",
                "NUMAC": "2024070101",
                "Titel": "Test Document 1",
                "DatumPublicatie": "2024-07-01T00:00:00Z",
                "Taal": "Nederlands",
                "DocumentUrlXML": "http://example.com/doc1.xml"
            }
        ]
        mock_client.download_xml.return_value = """
        <akomaNtoso>
            <act>
                <preface><docTitle>Test Document</docTitle></preface>
                <body>
                    <article id="art1">
                        <num>1</num>
                        <content><p>Test article content</p></content>
                    </article>
                </body>
            </act>
        </akomaNtoso>
        """
        mock_client_class.return_value = mock_client
        
        # Mock Voyage AI
        mock_voyage = Mock()
        mock_result = Mock()
        mock_result.embeddings = [[0.1] * 1024]
        mock_voyage.embed.return_value = mock_result
        mock_voyage_class.return_value = mock_voyage
        
        # Mock Pinecone
        mock_pc = Mock()
        mock_index = Mock()
        mock_index.upsert.return_value = {"upserted_count": 1}
        mock_pc.Index.return_value = mock_index
        mock_pinecone_class.return_value = mock_pc
        
        # Mock Neo4j
        mock_neo4j = Mock()
        mock_neo4j.upsert_act.return_value = True
        mock_neo4j.upsert_article.return_value = True
        mock_neo4j_class.return_value.__enter__.return_value = mock_neo4j


@pytest.mark.performance
class TestPerformance:
    """Performance and load tests."""
    
    def test_chunking_performance(self):
        """Test text chunking performance with large documents."""
        
        from sources.vlaamse_codex.ingest import VlaamseCodexIngester
        
        # Create large text
        large_text = "This is a test sentence. " * 10000  # ~250k characters
        
        ingester = VlaamseCodexIngester(dry_run=True)
        
        start_time = time.time()
        chunks = ingester._chunk_text(large_text)
        end_time = time.time()
        
        # Verify chunking completed in reasonable time
        assert end_time - start_time < 5.0  # Should complete in under 5 seconds
        assert len(chunks) > 0
        assert all(len(chunk) > 0 for chunk in chunks)
    
    def test_memory_usage_with_batch_processing(self):
        """Test memory usage doesn't grow excessively with batch processing."""
        
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Simulate processing many documents
        from sources.vlaamse_codex.ingest import VlaamseCodexIngester
        ingester = VlaamseCodexIngester(dry_run=True)
        
        # Process multiple batches
        for i in range(10):
            text = f"Test document {i} content. " * 1000
            chunks = ingester._chunk_text(text)
            assert len(chunks) > 0
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_growth = final_memory - initial_memory
        
        # Memory growth should be reasonable (less than 100MB for this test)
        assert memory_growth < 100, f"Memory grew by {memory_growth:.1f}MB"


if __name__ == "__main__":
    # Run smoke tests
    pytest.main([__file__, "-v", "-m", "e2e"])
