"""
Tests for checkpoint and idempotency functionality.
"""

import json
import pytest
import tempfile
from datetime import date, datetime
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from common.checkpoint import Checkpoint, LocalFileBackend, GCSBackend
from common.transaction import Transactional<PERSON>ngester, IngestionResult
from common.models import CommonAct, CommonArticle, create_vector_id, parse_vector_id


class TestCheckpoint:
    """Test checkpoint functionality."""
    
    def test_local_backend_read_write(self):
        """Test local file backend read/write operations."""
        with tempfile.TemporaryDirectory() as tmpdir:
            backend = LocalFileBackend(Path(tmpdir) / "test.json")
            
            # Test read from non-existent file
            data = backend.read()
            assert data == {}
            
            # Test write and read
            test_data = {"cursor": "2024-01-01", "count": 42}
            backend.write(test_data)
            
            read_data = backend.read()
            assert read_data == test_data
            
            # Test exists
            assert backend.exists()
            
            # Test delete
            backend.delete()
            assert not backend.exists()
    
    @patch('google.cloud.storage.Client')
    def test_gcs_backend_read_write(self, mock_storage_client):
        """Test GCS backend read/write operations."""
        # Setup mocks
        mock_client = Mock()
        mock_bucket = Mock()
        mock_blob = Mock()
        
        mock_storage_client.return_value = mock_client
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        
        backend = GCSBackend("test-bucket", "test/path.json")
        
        # Test read from non-existent blob
        mock_blob.exists.return_value = False
        data = backend.read()
        assert data == {}
        
        # Test write and read
        test_data = {"cursor": "2024-01-01", "count": 42}
        mock_blob.exists.return_value = True
        mock_blob.download_as_text.return_value = json.dumps(test_data)
        
        backend.write(test_data)
        read_data = backend.read()
        
        assert read_data == test_data
        mock_blob.upload_from_string.assert_called()
    
    def test_checkpoint_manager(self):
        """Test checkpoint manager functionality."""
        with tempfile.TemporaryDirectory() as tmpdir:
            # Force local backend
            with patch('common.checkpoint.config') as mock_config:
                mock_config.GCS_BUCKET = None
                mock_config.CHECKPOINTS_DIR = Path(tmpdir)
                
                checkpoint = Checkpoint("test_source")
                
                # Test initial read
                data = checkpoint.read()
                assert data == {}
                assert not checkpoint.exists()
                
                # Test write with metadata
                checkpoint.write({"cursor": "2024-01-01", "doc_id": "test123"})
                
                # Test read with metadata
                data = checkpoint.read()
                assert data["cursor"] == "2024-01-01"
                assert data["doc_id"] == "test123"
                assert data["source"] == "test_source"
                assert "updated_at" in data
                assert "version" in data
                
                # Test convenience methods
                cursor = checkpoint.get_cursor()
                assert cursor == "2024-01-01"
                
                checkpoint.update_cursor("2024-01-02", extra_field="value")
                cursor = checkpoint.get_cursor()
                assert cursor == "2024-01-02"
                
                data = checkpoint.read()
                assert data["extra_field"] == "value"
                
                # Test stats
                stats = checkpoint.get_stats()
                assert stats["source"] == "test_source"
                assert stats["exists"] is True
                assert stats["cursor"] == "2024-01-02"
                
                # Test delete
                checkpoint.delete()
                assert not checkpoint.exists()


class TestVectorIdGeneration:
    """Test deterministic vector ID generation."""
    
    def test_create_vector_id(self):
        """Test vector ID creation."""
        vector_id = create_vector_id("vlaamse", "act123", "art456", 2)
        assert vector_id == "vlaamse:act123:art456:2"
        
        # Test default chunk index
        vector_id = create_vector_id("eu_dump", "celex123", "art1")
        assert vector_id == "eu_dump:celex123:art1:0"
    
    def test_parse_vector_id(self):
        """Test vector ID parsing."""
        vector_id = "vlaamse:act123:art456:2"
        parsed = parse_vector_id(vector_id)
        
        assert parsed["source"] == "vlaamse"
        assert parsed["act_id"] == "act123"
        assert parsed["article_id"] == "art456"
        assert parsed["chunk_index"] == 2
        
        # Test invalid format
        with pytest.raises(ValueError):
            parse_vector_id("invalid:format")


class TestTransactionalIngester:
    """Test transactional ingestion functionality."""
    
    @patch('common.transaction.voyageai.Client')
    @patch('common.transaction.Pinecone')
    @patch('common.transaction.Neo4jClient')
    @patch('common.transaction.storage.Client')
    def test_dry_run_ingestion(self, mock_gcs, mock_neo4j, mock_pinecone, mock_voyage):
        """Test dry run ingestion."""
        ingester = TransactionalIngester("vlaamse", dry_run=True)
        
        # Create test data
        act = CommonAct(
            id="test_act",
            title="Test Act",
            date=date(2024, 1, 1),
            language="nl",
            source="vlaamse"
        )
        
        articles = [
            CommonArticle(
                id="test_act#art1",
                act_id="test_act",
                number="1",
                text="This is a test article with enough content to create chunks.",
                language="nl"
            )
        ]
        
        result = ingester.ingest_document_atomic(act, articles)
        
        assert result.success is True
        assert result.act_id == "test_act"
        assert result.articles_count == 1
        assert result.vectors_count > 0  # Should simulate chunking
    
    @patch('common.transaction.voyageai.Client')
    @patch('common.transaction.Pinecone')
    @patch('common.transaction.Neo4jClient')
    def test_atomic_ingestion_success(self, mock_neo4j_class, mock_pinecone_class, mock_voyage_class):
        """Test successful atomic ingestion."""
        # Setup mocks
        mock_voyage = Mock()
        mock_result = Mock()
        mock_result.embeddings = [[0.1] * 1024]
        mock_voyage.embed.return_value = mock_result
        mock_voyage_class.return_value = mock_voyage
        
        mock_pc = Mock()
        mock_index = Mock()
        mock_index.upsert.return_value = {"upserted_count": 1}
        mock_pc.Index.return_value = mock_index
        mock_pinecone_class.return_value = mock_pc
        
        mock_neo4j = Mock()
        mock_driver = Mock()
        mock_session = Mock()
        mock_tx = Mock()
        
        mock_neo4j.driver = mock_driver
        mock_driver.session.return_value.__enter__.return_value = mock_session
        mock_session.begin_transaction.return_value.__enter__.return_value = mock_tx
        mock_tx.run.return_value.single.return_value = {"id": "test"}
        
        mock_neo4j_class.return_value = mock_neo4j
        
        ingester = TransactionalIngester("vlaamse", dry_run=False, save_raw_files=False)
        
        # Create test data
        act = CommonAct(
            id="test_act",
            title="Test Act",
            date=date(2024, 1, 1),
            language="nl",
            source="vlaamse"
        )
        
        articles = [
            CommonArticle(
                id="test_act#art1",
                act_id="test_act",
                number="1",
                text="This is a test article with enough content to create chunks for embedding.",
                language="nl"
            )
        ]
        
        result = ingester.ingest_document_atomic(act, articles)
        
        assert result.success is True
        assert result.act_id == "test_act"
        assert result.articles_count == 1
        assert result.vectors_count > 0
        
        # Verify Neo4j transaction was used
        mock_session.begin_transaction.assert_called()
        mock_tx.commit.assert_called()
        
        # Verify Pinecone upsert was called
        mock_index.upsert.assert_called()
        
        # Verify deterministic vector IDs were used
        call_args = mock_index.upsert.call_args
        vectors = call_args[1]['vectors']
        assert len(vectors) > 0
        
        # Check vector ID format
        vector_id = vectors[0]['id']
        assert vector_id.startswith("vlaamse:test_act:test_act#art1:")
    
    @patch('common.transaction.Neo4jClient')
    def test_atomic_ingestion_rollback(self, mock_neo4j_class):
        """Test transaction rollback on failure."""
        # Setup mock to fail during transaction
        mock_neo4j = Mock()
        mock_driver = Mock()
        mock_session = Mock()
        mock_tx = Mock()
        
        mock_neo4j.driver = mock_driver
        mock_driver.session.return_value.__enter__.return_value = mock_session
        mock_session.begin_transaction.return_value.__enter__.return_value = mock_tx
        mock_tx.run.side_effect = Exception("Database error")
        
        mock_neo4j_class.return_value = mock_neo4j
        
        ingester = TransactionalIngester("vlaamse", dry_run=False, save_raw_files=False)
        
        # Create test data
        act = CommonAct(
            id="test_act",
            title="Test Act",
            date=date(2024, 1, 1),
            language="nl",
            source="vlaamse"
        )
        
        articles = [
            CommonArticle(
                id="test_act#art1",
                act_id="test_act",
                number="1",
                text="Test article",
                language="nl"
            )
        ]
        
        result = ingester.ingest_document_atomic(act, articles)
        
        assert result.success is False
        assert "Database error" in result.error
        
        # Verify rollback was called
        mock_tx.rollback.assert_called()


class TestIdempotency:
    """Test idempotent operations."""
    
    def test_vector_id_consistency(self):
        """Test that same input produces same vector ID."""
        # Same inputs should produce same ID
        id1 = create_vector_id("vlaamse", "act123", "art456", 1)
        id2 = create_vector_id("vlaamse", "act123", "art456", 1)
        assert id1 == id2
        
        # Different inputs should produce different IDs
        id3 = create_vector_id("vlaamse", "act123", "art456", 2)
        assert id1 != id3
        
        id4 = create_vector_id("vlaamse", "act124", "art456", 1)
        assert id1 != id4


@pytest.mark.integration
class TestCheckpointIntegration:
    """Integration tests for checkpoint functionality."""
    
    @patch('sources.vlaamse_codex.client.VlaamseCodexClient')
    @patch('sources.vlaamse_codex.parse.VlaamseCodexParser')
    @patch('common.transaction.TransactionalIngester')
    def test_resume_from_checkpoint(self, mock_ingester_class, mock_parser_class, mock_client_class):
        """Test resuming ingestion from checkpoint."""
        from sources.vlaamse_codex.ingest import VlaamseCodexIngester
        
        # Setup mocks
        mock_client = Mock()
        mock_client.get_documents.return_value = [
            {"Id": "doc1", "DatumPublicatie": "2024-01-01T00:00:00Z"},
            {"Id": "doc2", "DatumPublicatie": "2024-01-02T00:00:00Z"},
        ]
        mock_client_class.return_value = mock_client
        
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser
        
        mock_ingester = Mock()
        mock_ingester.ingest_document_atomic.return_value = IngestionResult(
            success=True, act_id="test", articles_count=1, vectors_count=1
        )
        mock_ingester_class.return_value = mock_ingester
        
        with tempfile.TemporaryDirectory() as tmpdir:
            with patch('common.checkpoint.config') as mock_config:
                mock_config.GCS_BUCKET = None
                mock_config.CHECKPOINTS_DIR = Path(tmpdir)
                
                # First run - process first document
                ingester = VlaamseCodexIngester(dry_run=False, resume=True)
                
                # Simulate processing only first document
                mock_client.get_documents.return_value = [
                    {"Id": "doc1", "DatumPublicatie": "2024-01-01T00:00:00Z"}
                ]
                
                stats = ingester.ingest_documents(limit=1)
                assert stats.documents_processed == 1
                
                # Check checkpoint was created
                checkpoint_status = ingester.get_checkpoint_status()
                assert checkpoint_status['exists'] is True
                
                # Second run - should resume from checkpoint
                ingester2 = VlaamseCodexIngester(dry_run=False, resume=True)
                
                # Mock should be called with cursor from checkpoint
                mock_client.get_documents.return_value = [
                    {"Id": "doc2", "DatumPublicatie": "2024-01-02T00:00:00Z"}
                ]
                
                stats2 = ingester2.ingest_documents(limit=1)
                assert stats2.documents_processed == 1
    
    def test_checkpoint_reset(self):
        """Test checkpoint reset functionality."""
        from sources.vlaamse_codex.ingest import VlaamseCodexIngester
        
        with tempfile.TemporaryDirectory() as tmpdir:
            with patch('common.checkpoint.config') as mock_config:
                mock_config.GCS_BUCKET = None
                mock_config.CHECKPOINTS_DIR = Path(tmpdir)
                
                ingester = VlaamseCodexIngester(dry_run=True)
                
                # Create a checkpoint
                ingester.checkpoint.write({"cursor": "2024-01-01", "doc_id": "test"})
                assert ingester.checkpoint.exists()
                
                # Reset checkpoint
                ingester.reset_checkpoint()
                assert not ingester.checkpoint.exists()
                
                # Status should show no checkpoint
                status = ingester.get_checkpoint_status()
                assert status['exists'] is False
