"""
Tests for global registry and audit functionality.
"""

import pytest
import tempfile
from datetime import date, datetime
from unittest.mock import Mock, patch, MagicMock

from common.registry import GlobalRegistry, RegistryEntry, ConsistencyStatus, calculate_sha256, create_gcs_uri
from common.transaction import TransactionalIngester
from tools.audit import Consistency<PERSON><PERSON><PERSON>, AuditStatus
from tools.repair import RepairTool


class TestRegistryEntry:
    """Test RegistryEntry data model."""
    
    def test_registry_entry_creation(self):
        """Test creating a registry entry."""
        entry = RegistryEntry(
            source="vlaamse",
            doc_id="test_doc_123",
            gcs_uri="gs://bucket/raw/vlaamse/2024/test_doc_123.xml",
            sha256="abc123",
            neo4j_loaded=True,
            pinecone_loaded=False,
            article_count=5
        )
        
        assert entry.source == "vlaamse"
        assert entry.doc_id == "test_doc_123"
        assert entry.neo4j_loaded is True
        assert entry.pinecone_loaded is False
        assert entry.article_count == 5


class TestUtilityFunctions:
    """Test utility functions."""
    
    def test_calculate_sha256(self):
        """Test SHA-256 calculation."""
        content = "test content"
        sha256 = calculate_sha256(content)
        
        assert len(sha256) == 64  # SHA-256 is 64 hex characters
        assert sha256 == calculate_sha256(content)  # Deterministic
        
        # Different content should produce different hash
        different_sha256 = calculate_sha256("different content")
        assert sha256 != different_sha256
    
    def test_create_gcs_uri(self):
        """Test GCS URI creation."""
        uri = create_gcs_uri("vlaamse", "test_act_123", 2024, "xml")
        expected = "gs://ailex-be/raw/vlaamse/2024/test_act_123.xml"
        assert uri == expected
        
        # Test with different extension
        uri_pdf = create_gcs_uri("vlaamse", "test_act_123", 2024, "pdf")
        expected_pdf = "gs://ailex-be/raw/vlaamse/2024/test_act_123.pdf"
        assert uri_pdf == expected_pdf


@patch('common.registry.create_client')
class TestGlobalRegistry:
    """Test GlobalRegistry operations."""
    
    def test_registry_initialization(self, mock_create_client):
        """Test registry initialization."""
        mock_client = Mock()
        mock_create_client.return_value = mock_client
        
        registry = GlobalRegistry("http://test.supabase.co", "test_key")
        
        assert registry.supabase_url == "http://test.supabase.co"
        assert registry.supabase_key == "test_key"
        assert registry.client == mock_client
        mock_create_client.assert_called_once_with("http://test.supabase.co", "test_key")
    
    def test_exists_check(self, mock_create_client):
        """Test document existence check."""
        mock_client = Mock()
        mock_table = Mock()
        mock_query = Mock()
        
        mock_create_client.return_value = mock_client
        mock_client.table.return_value = mock_table
        mock_table.select.return_value = mock_query
        mock_query.eq.return_value = mock_query
        
        # Test document exists and is fully processed
        mock_query.execute.return_value.data = [{
            "source": "vlaamse",
            "doc_id": "test_123",
            "sha256": "abc123",
            "neo4j_loaded": True,
            "pinecone_loaded": True
        }]
        
        registry = GlobalRegistry("http://test.supabase.co", "test_key")
        exists = registry.exists("vlaamse", "test_123", "abc123")
        
        assert exists is True
        
        # Test document exists but not fully processed
        mock_query.execute.return_value.data = [{
            "source": "vlaamse",
            "doc_id": "test_123",
            "sha256": "abc123",
            "neo4j_loaded": True,
            "pinecone_loaded": False
        }]
        
        exists = registry.exists("vlaamse", "test_123", "abc123")
        assert exists is False
        
        # Test document doesn't exist
        mock_query.execute.return_value.data = []
        exists = registry.exists("vlaamse", "test_123", "abc123")
        assert exists is False
    
    def test_upsert_entry(self, mock_create_client):
        """Test registry entry upsert."""
        mock_client = Mock()
        mock_table = Mock()
        
        mock_create_client.return_value = mock_client
        mock_client.table.return_value = mock_table
        mock_table.upsert.return_value = mock_table
        mock_table.execute.return_value.data = [{"id": "test_id"}]
        
        registry = GlobalRegistry("http://test.supabase.co", "test_key")
        
        entry = RegistryEntry(
            source="vlaamse",
            doc_id="test_123",
            gcs_uri="gs://bucket/test.xml",
            sha256="abc123",
            neo4j_loaded=True,
            pinecone_loaded=True,
            article_count=5
        )
        
        success = registry.upsert(entry)
        
        assert success is True
        mock_table.upsert.assert_called_once()
        
        # Verify the data passed to upsert
        call_args = mock_table.upsert.call_args[0][0]
        assert call_args["source"] == "vlaamse"
        assert call_args["doc_id"] == "test_123"
        assert call_args["neo4j_loaded"] is True
        assert call_args["pinecone_loaded"] is True
    
    def test_update_flags(self, mock_create_client):
        """Test updating specific flags."""
        mock_client = Mock()
        mock_table = Mock()
        
        mock_create_client.return_value = mock_client
        mock_client.table.return_value = mock_table
        mock_table.update.return_value = mock_table
        mock_table.eq.return_value = mock_table
        mock_table.execute.return_value.data = [{"id": "test_id"}]
        
        registry = GlobalRegistry("http://test.supabase.co", "test_key")
        
        success = registry.update_flags(
            "vlaamse", 
            "test_123",
            neo4j_loaded=True,
            article_count=10
        )
        
        assert success is True
        mock_table.update.assert_called_once_with({
            "neo4j_loaded": True,
            "article_count": 10
        })


class TestTransactionalIngesterWithRegistry:
    """Test transactional ingester with registry integration."""
    
    @patch('common.transaction.GlobalRegistry')
    @patch('common.transaction.voyageai.Client')
    @patch('common.transaction.Pinecone')
    @patch('common.transaction.Neo4jClient')
    def test_registry_integration(self, mock_neo4j, mock_pinecone, mock_voyage, mock_registry_class):
        """Test that ingester properly integrates with registry."""
        # Setup mocks
        mock_registry = Mock()
        mock_registry.exists.return_value = False
        mock_registry.upsert.return_value = True
        mock_registry.update_flags.return_value = True
        mock_registry_class.return_value = mock_registry
        
        # Setup other mocks
        mock_voyage_client = Mock()
        mock_result = Mock()
        mock_result.embeddings = [[0.1] * 1024]
        mock_voyage_client.embed.return_value = mock_result
        mock_voyage.return_value = mock_voyage_client
        
        mock_pc = Mock()
        mock_index = Mock()
        mock_index.upsert.return_value = {"upserted_count": 1}
        mock_pc.Index.return_value = mock_index
        mock_pinecone.return_value = mock_pc
        
        mock_neo4j_client = Mock()
        mock_driver = Mock()
        mock_session = Mock()
        mock_tx = Mock()
        
        mock_neo4j_client.driver = mock_driver
        mock_driver.session.return_value.__enter__.return_value = mock_session
        mock_session.begin_transaction.return_value.__enter__.return_value = mock_tx
        mock_tx.run.return_value.single.return_value = {"id": "test"}
        
        mock_neo4j.return_value = mock_neo4j_client
        
        # Test ingestion
        ingester = TransactionalIngester("vlaamse", dry_run=False, use_registry=True)
        
        from common.models import CommonAct, CommonArticle
        
        act = CommonAct(
            id="test_act",
            title="Test Act",
            date=date(2024, 1, 1),
            language="nl",
            source="vlaamse"
        )
        
        articles = [
            CommonArticle(
                id="test_act#art1",
                act_id="test_act",
                number="1",
                text="Test article content",
                language="nl"
            )
        ]
        
        result = ingester.ingest_document_atomic(act, articles, raw_content="<xml>test</xml>")
        
        assert result.success is True
        
        # Verify registry operations
        mock_registry.exists.assert_called_once()
        mock_registry.upsert.assert_called()
        
        # Verify flags were updated
        assert mock_registry.update_flags.call_count == 2  # Once for Neo4j, once for Pinecone
        
        # Verify Neo4j and Pinecone operations
        mock_tx.run.assert_called()
        mock_index.upsert.assert_called()


@patch('tools.audit.GlobalRegistry')
@patch('tools.audit.Neo4jClient')
@patch('tools.audit.Pinecone')
class TestConsistencyAuditor:
    """Test consistency auditor."""
    
    def test_audit_consistent_entry(self, mock_pinecone_class, mock_neo4j_class, mock_registry_class):
        """Test auditing a consistent entry."""
        # Setup mocks
        mock_registry = Mock()
        mock_registry.list_entries.return_value = [
            RegistryEntry(
                id="test_id",
                source="vlaamse",
                doc_id="test_123",
                gcs_uri="gs://bucket/test.xml",
                sha256="abc123",
                neo4j_loaded=True,
                pinecone_loaded=True,
                article_count=5
            )
        ]
        mock_registry_class.return_value = mock_registry
        
        mock_neo4j = Mock()
        mock_driver = Mock()
        mock_session = Mock()
        mock_session.run.return_value.single.return_value = {"exists": True}
        mock_driver.session.return_value.__enter__.return_value = mock_session
        mock_neo4j.driver = mock_driver
        mock_neo4j_class.return_value = mock_neo4j
        
        mock_pc = Mock()
        mock_index = Mock()
        mock_index.query.return_value.matches = [Mock()] * 5  # 5 vectors
        mock_pc.Index.return_value = mock_index
        mock_pinecone_class.return_value = mock_pc
        
        # Test audit
        auditor = ConsistencyAuditor()
        
        with patch.object(auditor, '_check_gcs', return_value=(True, True)):
            results = auditor.audit_entries(source="vlaamse", limit=10)
        
        assert len(results) == 1
        result = results[0]
        assert result.audit_status == AuditStatus.OK
        assert result.source == "vlaamse"
        assert result.doc_id == "test_123"
    
    def test_audit_missing_node(self, mock_pinecone_class, mock_neo4j_class, mock_registry_class):
        """Test auditing entry with missing Neo4j node."""
        # Setup mocks
        mock_registry = Mock()
        mock_registry.list_entries.return_value = [
            RegistryEntry(
                id="test_id",
                source="vlaamse",
                doc_id="test_123",
                gcs_uri="gs://bucket/test.xml",
                sha256="abc123",
                neo4j_loaded=True,
                pinecone_loaded=True,
                article_count=5
            )
        ]
        mock_registry_class.return_value = mock_registry
        
        mock_neo4j = Mock()
        mock_driver = Mock()
        mock_session = Mock()
        mock_session.run.return_value.single.return_value = {"exists": False}  # Node missing
        mock_driver.session.return_value.__enter__.return_value = mock_session
        mock_neo4j.driver = mock_driver
        mock_neo4j_class.return_value = mock_neo4j
        
        # Test audit
        auditor = ConsistencyAuditor()
        
        with patch.object(auditor, '_check_gcs', return_value=(True, True)):
            results = auditor.audit_entries(source="vlaamse", limit=10)
        
        assert len(results) == 1
        result = results[0]
        assert result.audit_status == AuditStatus.MISSING_NODE
        assert "Neo4j node not found" in result.error_message


@patch('tools.repair.GlobalRegistry')
class TestRepairTool:
    """Test repair tool functionality."""
    
    def test_repair_dry_run(self, mock_registry_class):
        """Test repair in dry run mode."""
        mock_registry = Mock()
        mock_registry.get_inconsistent_entries.return_value = [
            RegistryEntry(
                id="test_id",
                source="vlaamse",
                doc_id="test_123",
                gcs_uri="gs://bucket/test.xml",
                neo4j_loaded=False,
                pinecone_loaded=True,
                article_count=5
            )
        ]
        mock_registry_class.return_value = mock_registry
        
        repair_tool = RepairTool(dry_run=True)
        
        with patch.object(repair_tool, '_download_from_gcs', return_value="<xml>test</xml>"):
            with patch.object(repair_tool, 'parsers', {"vlaamse": Mock()}):
                repair_tool.parsers["vlaamse"].parse_document.return_value = (Mock(), [Mock()])
                
                results = repair_tool.repair_all_inconsistencies(source="vlaamse", limit=10)
        
        assert len(results) == 1
        result = results[0]
        assert result.success is True
        assert result.source == "vlaamse"
        assert result.doc_id == "test_123"


class TestIntegrationScenarios:
    """Test integration scenarios."""
    
    @patch('common.registry.create_client')
    def test_registry_exists_early_exit(self, mock_create_client):
        """Test that ingester exits early if document already exists in registry."""
        mock_client = Mock()
        mock_table = Mock()
        mock_query = Mock()
        
        mock_create_client.return_value = mock_client
        mock_client.table.return_value = mock_table
        mock_table.select.return_value = mock_query
        mock_query.eq.return_value = mock_query
        
        # Document exists and is fully processed
        mock_query.execute.return_value.data = [{
            "source": "vlaamse",
            "doc_id": "test_123",
            "sha256": "abc123",
            "neo4j_loaded": True,
            "pinecone_loaded": True
        }]
        
        ingester = TransactionalIngester("vlaamse", dry_run=False, use_registry=True)
        
        from common.models import CommonAct, CommonArticle
        
        act = CommonAct(
            id="test_123",
            title="Test Act",
            date=date(2024, 1, 1),
            language="nl",
            source="vlaamse"
        )
        
        articles = [CommonArticle(
            id="test_123#art1",
            act_id="test_123",
            number="1",
            text="Test article",
            language="nl"
        )]
        
        result = ingester.ingest_document_atomic(act, articles, raw_content="<xml>test</xml>")
        
        # Should succeed immediately without processing
        assert result.success is True
        assert result.vectors_count == 0  # No new vectors created
