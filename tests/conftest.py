"""
Pytest configuration and fixtures for ailex-be-ingest tests.
"""

import os
import pytest
from datetime import date
from unittest.mock import Mock, MagicMock

# Set test environment
os.environ["TEST_MODE"] = "true"
os.environ["NEO4J_PASSWORD"] = "test_password"
os.environ["VOYAGE_API_KEY"] = "test_voyage_key"
os.environ["PINECONE_API_KEY"] = "test_pinecone_key"


@pytest.fixture
def sample_vlaamse_metadata():
    """Sample metadata from Vlaamse Codex API."""
    return {
        "Id": "test_doc_123",
        "NUMAC": "2024070123",
        "Titel": "Koninklijk besluit betreffende testregeling",
        "Type": "Koninklijk besluit",
        "DatumPublicatie": "2024-07-25T00:00:00Z",
        "Taal": "Nederlands",
        "DocumentUrlXML": "https://codex.vlaanderen.be/xml/test_doc_123.xml",
        "ELI": "eli:be-vlg:kb:2024:07:25:test"
    }


@pytest.fixture
def sample_vlaamse_xml():
    """Sample XML content from Vlaamse Codex."""
    return """<?xml version="1.0" encoding="UTF-8"?>
<akomaNtoso xmlns="http://docs.oasis-open.org/legaldocml/ns/akoma-ntoso/1.0">
    <act>
        <meta>
            <identification>
                <FRBRWork>
                    <FRBRuri value="eli:be-vlg:kb:2024:07:25:test"/>
                </FRBRWork>
            </identification>
        </meta>
        <preface>
            <docTitle>Koninklijk besluit betreffende testregeling</docTitle>
        </preface>
        <body>
            <article id="art1">
                <num>1</num>
                <heading>Algemene bepalingen</heading>
                <content>
                    <p>Dit artikel bevat de algemene bepalingen van de testregeling.</p>
                </content>
            </article>
            <article id="art2">
                <num>2</num>
                <heading>Specifieke bepalingen</heading>
                <content>
                    <p>Dit artikel bevat specifieke bepalingen voor de implementatie.</p>
                </content>
            </article>
        </body>
    </act>
</akomaNtoso>"""


@pytest.fixture
def mock_neo4j_client():
    """Mock Neo4j client for testing."""
    mock_client = Mock()
    mock_client.upsert_act.return_value = True
    mock_client.upsert_article.return_value = True
    mock_client.create_relationship.return_value = True
    mock_client.get_act_stats.return_value = {"vlaamse": 10}
    mock_client.get_article_stats.return_value = {"nl": 25}
    return mock_client


@pytest.fixture
def mock_voyage_client():
    """Mock Voyage AI client for testing."""
    mock_client = Mock()
    mock_result = Mock()
    mock_result.embeddings = [[0.1] * 1024, [0.2] * 1024]  # Mock embeddings
    mock_client.embed.return_value = mock_result
    return mock_client


@pytest.fixture
def mock_pinecone_index():
    """Mock Pinecone index for testing."""
    mock_index = Mock()
    mock_index.upsert.return_value = {"upserted_count": 2}
    mock_index.query.return_value = Mock(matches=[])
    return mock_index


@pytest.fixture
def mock_gcs_bucket():
    """Mock Google Cloud Storage bucket for testing."""
    mock_bucket = Mock()
    mock_blob = Mock()
    mock_bucket.blob.return_value = mock_blob
    return mock_bucket


@pytest.fixture
def sample_common_act():
    """Sample CommonAct instance for testing."""
    from common.models import CommonAct
    
    return CommonAct(
        id="vlaamse_2024070123",
        title="Koninklijk besluit betreffende testregeling",
        date=date(2024, 7, 25),
        language="nl",
        source="vlaamse",
        eli="eli:be-vlg:kb:2024:07:25:test",
        metadata={
            "numac": "2024070123",
            "type": "Koninklijk besluit",
            "publication_date": "2024-07-25"
        }
    )


@pytest.fixture
def sample_common_articles():
    """Sample CommonArticle instances for testing."""
    from common.models import CommonArticle
    
    return [
        CommonArticle(
            id="vlaamse_2024070123#art1",
            act_id="vlaamse_2024070123",
            number="1",
            heading="Algemene bepalingen",
            text="Dit artikel bevat de algemene bepalingen van de testregeling.",
            language="nl",
            metadata={"element_tag": "article", "has_heading": True}
        ),
        CommonArticle(
            id="vlaamse_2024070123#art2",
            act_id="vlaamse_2024070123",
            number="2",
            heading="Specifieke bepalingen",
            text="Dit artikel bevat specifieke bepalingen voor de implementatie.",
            language="nl",
            metadata={"element_tag": "article", "has_heading": True}
        )
    ]
