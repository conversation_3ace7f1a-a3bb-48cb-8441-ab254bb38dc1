#!/usr/bin/env python3
"""
Comprehensive Neo4j diagnostic test for ailex-be-ingest.
"""

import sys
import socket
import ssl
import time
from pathlib import Path
from urllib.parse import urlparse

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from common.config import config, setup_logging

def test_network_connectivity():
    """Test basic network connectivity to Neo4j server."""
    print("🌐 Testing network connectivity...")
    
    try:
        # Parse the URI to get hostname and port
        parsed = urlparse(config.NEO4J_URI)
        hostname = parsed.hostname
        port = parsed.port or 7687
        
        print(f"   Hostname: {hostname}")
        print(f"   Port: {port}")
        
        # Test basic TCP connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((hostname, port))
        sock.close()
        
        if result == 0:
            print("✅ TCP connection successful")
            return True
        else:
            print(f"❌ TCP connection failed (error code: {result})")
            return False
            
    except Exception as e:
        print(f"❌ Network test failed: {e}")
        return False

def test_ssl_connection():
    """Test SSL connection to Neo4j server."""
    print("🔒 Testing SSL connection...")
    
    try:
        parsed = urlparse(config.NEO4J_URI)
        hostname = parsed.hostname
        port = parsed.port or 7687
        
        # Create SSL context
        context = ssl.create_default_context()
        
        # Test SSL connection
        with socket.create_connection((hostname, port), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                print(f"✅ SSL connection successful")
                print(f"   SSL version: {ssock.version()}")
                print(f"   Cipher: {ssock.cipher()}")
                return True
                
    except Exception as e:
        print(f"❌ SSL connection failed: {e}")
        return False

def test_neo4j_driver_versions():
    """Test Neo4j driver installation and version."""
    print("📦 Testing Neo4j driver...")
    
    try:
        import neo4j
        print(f"✅ Neo4j driver installed")
        print(f"   Version: {neo4j.__version__}")
        
        # Test driver creation (without connecting)
        from neo4j import GraphDatabase
        driver = GraphDatabase.driver(
            config.NEO4J_URI,
            auth=(config.NEO4J_USERNAME, config.NEO4J_PASSWORD),
            connection_timeout=5
        )
        print("✅ Driver created successfully")
        driver.close()
        return True
        
    except ImportError as e:
        print(f"❌ Neo4j driver not installed: {e}")
        return False
    except Exception as e:
        print(f"❌ Driver creation failed: {e}")
        return False

def test_auth_with_different_timeouts():
    """Test authentication with different timeout settings."""
    print("🔐 Testing authentication with various timeouts...")
    
    try:
        from neo4j import GraphDatabase
        
        timeouts = [5, 10, 30]
        
        for timeout in timeouts:
            print(f"   Trying with {timeout}s timeout...")
            try:
                driver = GraphDatabase.driver(
                    config.NEO4J_URI,
                    auth=(config.NEO4J_USERNAME, config.NEO4J_PASSWORD),
                    connection_timeout=timeout,
                    max_connection_lifetime=timeout * 2
                )
                
                with driver.session() as session:
                    result = session.run("RETURN 1")
                    record = result.single()
                    if record[0] == 1:
                        print(f"✅ Authentication successful with {timeout}s timeout")
                        driver.close()
                        return True
                        
                driver.close()
                
            except Exception as e:
                print(f"   ❌ Failed with {timeout}s timeout: {e}")
                continue
                
        return False
        
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        return False

def main():
    """Main diagnostic function."""
    setup_logging()
    
    print("🔍 Neo4j Comprehensive Diagnostic Test")
    print("=" * 50)
    
    print(f"Configuration:")
    print(f"   URI: {config.NEO4J_URI}")
    print(f"   Username: {config.NEO4J_USERNAME}")
    print(f"   Password: {'*' * len(config.NEO4J_PASSWORD)}")
    print()
    
    tests = [
        ("Network Connectivity", test_network_connectivity),
        ("SSL Connection", test_ssl_connection),
        ("Neo4j Driver", test_neo4j_driver_versions),
        ("Authentication", test_auth_with_different_timeouts),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results[test_name] = False
        print()
    
    # Summary
    print("📊 Test Summary")
    print("=" * 30)
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All diagnostic tests passed!")
    else:
        print("⚠️  Some tests failed. Check the details above.")
        print("\n🔧 Next steps:")
        print("   1. Verify Neo4j Aura instance is running")
        print("   2. Check credentials in Neo4j Console")
        print("   3. Ensure firewall/network allows connections")
        print("   4. Try resetting the database password")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
