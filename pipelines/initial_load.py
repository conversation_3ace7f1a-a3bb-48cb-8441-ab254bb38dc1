#!/usr/bin/env python3
"""
Initial load pipeline for ailex-be-ingest.

Orchestrates the complete ingestion process for all three phases:
1. Phase A: Vlaamse Codex
2. Phase B: EUR-Lex bulk dump (placeholder)
3. Phase C: CELLAR SPARQL + REST (placeholder)

Uses Prefect for workflow orchestration with proper error handling,
retries, and monitoring.
"""

import sys
import logging
from datetime import date, datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any

from prefect import flow, task, get_run_logger
from prefect.task_runners import ConcurrentTaskRunner

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from common.config import config, setup_logging
from sources.vlaamse_codex.ingest import VlaamseCodexIngester, IngestionStats

setup_logging()


@task(retries=3, retry_delay_seconds=60)
def setup_infrastructure() -> bool:
    """
    Set up required infrastructure (Neo4j schema, etc.).
    
    Returns:
        bool: True if setup successful
    """
    logger = get_run_logger()
    logger.info("Setting up infrastructure...")
    
    try:
        # Import here to avoid circular imports
        from scripts.setup_neo4j import setup_neo4j_schema, verify_connection
        
        # Verify Neo4j connection
        if not verify_connection():
            logger.error("Neo4j connection failed")
            return False
        
        # Setup schema
        if not setup_neo4j_schema():
            logger.error("Neo4j schema setup failed")
            return False
        
        logger.info("Infrastructure setup completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Infrastructure setup failed: {e}")
        raise


@task(retries=2, retry_delay_seconds=120)
def ingest_vlaamse_codex(
    limit: Optional[int] = None,
    since_date: Optional[date] = None,
    dry_run: bool = False,
    resume: bool = True,
    reset_checkpoint: bool = False
) -> Dict[str, Any]:
    """
    Ingest documents from Vlaamse Codex with checkpointing.

    Args:
        limit: Maximum number of documents to process
        since_date: Start date for ingestion
        dry_run: If True, don't write to databases
        resume: Whether to resume from checkpoint
        reset_checkpoint: Whether to reset checkpoint before starting

    Returns:
        Dictionary with ingestion statistics
    """
    logger = get_run_logger()
    logger.info(f"Starting Vlaamse Codex ingestion (limit={limit}, since={since_date}, dry_run={dry_run}, resume={resume})")

    try:
        ingester = VlaamseCodexIngester(dry_run=dry_run, resume=resume)

        # Reset checkpoint if requested
        if reset_checkpoint:
            logger.info("Resetting checkpoint...")
            ingester.reset_checkpoint()

        # Show checkpoint status
        if resume and not reset_checkpoint:
            status = ingester.get_checkpoint_status()
            if status['exists']:
                logger.info(f"Resuming from checkpoint: {status['cursor']}")
            else:
                logger.info("No checkpoint found, starting fresh")

        stats = ingester.ingest_documents(cursor_date=since_date, limit=limit)
        
        result = {
            "source": "vlaamse_codex",
            "documents_processed": stats.documents_processed,
            "documents_failed": stats.documents_failed,
            "acts_created": stats.acts_created,
            "articles_created": stats.articles_created,
            "vectors_created": stats.vectors_created,
            "files_saved": stats.files_saved,
            "duration_seconds": (stats.end_time - stats.start_time).total_seconds() if stats.end_time else 0
        }
        
        logger.info(f"Vlaamse Codex ingestion completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Vlaamse Codex ingestion failed: {e}")
        raise


@task
def ingest_eurlex_bulk(dry_run: bool = False) -> Dict[str, Any]:
    """
    Placeholder for EUR-Lex bulk dump ingestion.
    
    Args:
        dry_run: If True, don't write to databases
        
    Returns:
        Dictionary with ingestion statistics
    """
    logger = get_run_logger()
    logger.info("EUR-Lex bulk dump ingestion - PLACEHOLDER")
    
    # TODO: Implement EUR-Lex bulk dump processing
    return {
        "source": "eurlex_bulk",
        "documents_processed": 0,
        "message": "Not implemented yet - Phase B"
    }


@task
def ingest_cellar_delta(dry_run: bool = False) -> Dict[str, Any]:
    """
    Placeholder for CELLAR SPARQL + REST delta ingestion.
    
    Args:
        dry_run: If True, don't write to databases
        
    Returns:
        Dictionary with ingestion statistics
    """
    logger = get_run_logger()
    logger.info("CELLAR delta ingestion - PLACEHOLDER")
    
    # TODO: Implement CELLAR SPARQL + REST processing
    return {
        "source": "cellar_delta",
        "documents_processed": 0,
        "message": "Not implemented yet - Phase C"
    }


@task
def validate_results(results: list[Dict[str, Any]], min_documents: int = 1) -> bool:
    """
    Validate ingestion results.
    
    Args:
        results: List of ingestion results from all phases
        min_documents: Minimum number of documents expected
        
    Returns:
        bool: True if validation passes
    """
    logger = get_run_logger()
    logger.info("Validating ingestion results...")
    
    total_documents = sum(r.get("documents_processed", 0) for r in results)
    total_failed = sum(r.get("documents_failed", 0) for r in results)
    
    logger.info(f"Total documents processed: {total_documents}")
    logger.info(f"Total documents failed: {total_failed}")
    
    if total_documents < min_documents:
        logger.error(f"Insufficient documents processed: {total_documents} < {min_documents}")
        return False
    
    # Check failure rate
    if total_documents > 0:
        failure_rate = total_failed / (total_documents + total_failed)
        if failure_rate > 0.1:  # More than 10% failure rate
            logger.warning(f"High failure rate: {failure_rate:.2%}")
    
    logger.info("Validation passed")
    return True


@flow(
    name="ailex-be-initial-load",
    description="Complete initial load pipeline for ailex-be-ingest",
    task_runner=ConcurrentTaskRunner()
)
def initial_load_flow(
    limit: Optional[int] = None,
    since_days: Optional[int] = None,
    dry_run: bool = False,
    skip_infrastructure: bool = False,
    vlaamse_only: bool = False,
    resume: bool = True,
    reset_checkpoint: bool = False
) -> Dict[str, Any]:
    """
    Main flow for initial data loading.
    
    Args:
        limit: Maximum number of documents to process per source
        since_days: Number of days back to start ingestion from
        dry_run: If True, don't write to databases
        skip_infrastructure: If True, skip infrastructure setup
        vlaamse_only: If True, only run Vlaamse Codex ingestion
        
    Returns:
        Dictionary with overall results
    """
    logger = get_run_logger()
    logger.info("Starting ailex-be-ingest initial load pipeline")
    logger.info(f"Parameters: limit={limit}, since_days={since_days}, dry_run={dry_run}")
    
    # Calculate since date
    since_date = None
    if since_days:
        since_date = date.today() - timedelta(days=since_days)
        logger.info(f"Processing documents since: {since_date}")
    
    results = []
    
    try:
        # Phase 0: Infrastructure setup
        if not skip_infrastructure:
            setup_success = setup_infrastructure()
            if not setup_success:
                raise Exception("Infrastructure setup failed")
        
        # Phase A: Vlaamse Codex
        vlaamse_result = ingest_vlaamse_codex(
            limit=limit,
            since_date=since_date,
            dry_run=dry_run,
            resume=resume,
            reset_checkpoint=reset_checkpoint
        )
        results.append(vlaamse_result)
        
        # Phase B & C: EUR-Lex and CELLAR (only if not vlaamse_only)
        if not vlaamse_only:
            eurlex_result = ingest_eurlex_bulk(dry_run=dry_run)
            results.append(eurlex_result)
            
            cellar_result = ingest_cellar_delta(dry_run=dry_run)
            results.append(cellar_result)
        
        # Validation
        min_docs = 1 if not dry_run else 0
        validation_success = validate_results(results, min_documents=min_docs)
        
        # Summary
        total_processed = sum(r.get("documents_processed", 0) for r in results)
        total_failed = sum(r.get("documents_failed", 0) for r in results)
        
        summary = {
            "success": validation_success,
            "total_documents_processed": total_processed,
            "total_documents_failed": total_failed,
            "phases_completed": len(results),
            "results_by_phase": results,
            "dry_run": dry_run
        }
        
        logger.info(f"Pipeline completed: {summary}")
        return summary
        
    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        raise


def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run ailex-be-ingest initial load pipeline")
    parser.add_argument("--limit", type=int, help="Limit number of documents per source")
    parser.add_argument("--since-days", type=int, help="Number of days back to start from")
    parser.add_argument("--dry-run", action="store_true", help="Don't write to databases")
    parser.add_argument("--skip-infrastructure", action="store_true", help="Skip infrastructure setup")
    parser.add_argument("--vlaamse-only", action="store_true", help="Only run Vlaamse Codex ingestion")
    parser.add_argument("--resume", action="store_true", default=True, help="Resume from checkpoint (default: True)")
    parser.add_argument("--no-resume", dest="resume", action="store_false", help="Don't resume from checkpoint")
    parser.add_argument("--reset", action="store_true", help="Reset checkpoint before starting")

    args = parser.parse_args()
    
    # Validate configuration
    missing_vars = config.validate()
    if missing_vars:
        print(f"❌ Missing required environment variables: {missing_vars}")
        sys.exit(1)
    
    print("🚀 Starting ailex-be-ingest initial load pipeline")
    print("=" * 60)
    
    try:
        # Run the flow
        result = initial_load_flow(
            limit=args.limit,
            since_days=args.since_days,
            dry_run=args.dry_run,
            skip_infrastructure=args.skip_infrastructure,
            vlaamse_only=args.vlaamse_only,
            resume=args.resume,
            reset_checkpoint=args.reset
        )
        
        if result["success"]:
            print("✅ Pipeline completed successfully!")
            print(f"📊 Total documents processed: {result['total_documents_processed']}")
            if result["total_documents_failed"] > 0:
                print(f"⚠️  Documents failed: {result['total_documents_failed']}")
        else:
            print("❌ Pipeline completed with errors")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Pipeline failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
