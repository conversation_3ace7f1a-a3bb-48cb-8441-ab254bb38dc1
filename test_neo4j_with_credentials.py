#!/usr/bin/env python3
"""
Test Neo4j connection with custom credentials.
Use this script to test new credentials without modifying the .env file.
"""

import sys
import time
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from common.config import setup_logging

def test_neo4j_with_credentials(uri, username, password):
    """Test Neo4j connection with provided credentials."""
    print("🔍 Testing Neo4j Connection with provided credentials...")
    print(f"   URI: {uri}")
    print(f"   Username: {username}")
    print(f"   Password: {'*' * len(password)}")
    print()
    
    try:
        from neo4j import GraphDatabase
        
        print("📡 Creating driver...")
        driver = GraphDatabase.driver(uri, auth=(username, password))
        
        print("🔍 Testing connection...")
        with driver.session() as session:
            # Test basic query
            result = session.run("RETURN 'Hello Neo4j!' as message, datetime() as timestamp")
            record = result.single()
            message = record["message"]
            timestamp = record["timestamp"]
            
            print(f"✅ Connection successful: {message}")
            print(f"   Server timestamp: {timestamp}")
            
            # Get Neo4j version
            result = session.run("CALL dbms.components() YIELD name, versions, edition")
            for record in result:
                if record["name"] == "Neo4j Kernel":
                    version = record["versions"][0]
                    edition = record["edition"]
                    print(f"   Neo4j version: {version} ({edition})")
                    break
            
            # Get database stats
            result = session.run("MATCH (n) RETURN count(n) as node_count")
            node_count = result.single()["node_count"]
            print(f"   Total nodes: {node_count}")
            
            result = session.run("MATCH ()-[r]->() RETURN count(r) as rel_count")
            rel_count = result.single()["rel_count"]
            print(f"   Total relationships: {rel_count}")
            
            # Test write operation (create a test node)
            print("🔍 Testing write operation...")
            result = session.run("""
                CREATE (test:TestNode {
                    id: 'connection_test_' + toString(datetime().epochMillis),
                    created_at: datetime(),
                    message: 'Connection test successful'
                })
                RETURN test.id as test_id
            """)
            test_id = result.single()["test_id"]
            print(f"✅ Write test successful, created node: {test_id}")
            
            # Clean up test node
            session.run("MATCH (test:TestNode {id: $test_id}) DELETE test", {"test_id": test_id})
            print("✅ Test node cleaned up")
        
        driver.close()
        print("\n🎉 All tests passed! Neo4j connection is working perfectly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Connection failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False

def main():
    """Main function with interactive credential input."""
    setup_logging()
    
    print("🚀 Neo4j Connection Test with Custom Credentials")
    print("=" * 60)
    print()
    
    # Get credentials from user
    print("Please enter your Neo4j credentials:")
    uri = input("URI (e.g., neo4j+s://xxxxx.databases.neo4j.io): ").strip()
    username = input("Username (usually 'neo4j'): ").strip()
    password = input("Password: ").strip()
    
    if not all([uri, username, password]):
        print("❌ All fields are required!")
        return False
    
    print("\n⏳ Waiting 5 seconds before testing...")
    time.sleep(5)
    
    success = test_neo4j_with_credentials(uri, username, password)
    
    if success:
        print("\n✅ You can now update your .env file with these credentials:")
        print(f"NEO4J_URI={uri}")
        print(f"NEO4J_USERNAME={username}")
        print(f"NEO4J_PASSWORD={password}")
    else:
        print("\n❌ Please check your credentials and try again.")
        print("💡 Tips:")
        print("   - Make sure you're using the correct URI from Neo4j Aura")
        print("   - Verify username and password in Neo4j Console")
        print("   - Check if your database instance is running")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
