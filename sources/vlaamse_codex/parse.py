"""
XML parser for Vlaamse Codex documents.

Parses XML documents downloaded from DocumentUrlXML and converts them
to CommonAct and CommonArticle models for unified processing.
"""

import re
import logging
from datetime import date, datetime
from typing import List, Optional, Tuple, Dict, Any
from xml.etree.ElementTree import ParseError

from lxml import etree, html
from lxml.etree import _Element

from common.models import CommonAct, CommonArticle

logger = logging.getLogger(__name__)


class VlaamseCodexParser:
    """Parser for Vlaamse Codex XML documents."""
    
    def __init__(self):
        """Initialize the parser."""
        self.namespaces = {
            'akn': 'http://docs.oasis-open.org/legaldocml/ns/akoma-ntoso/1.0',
            'html': 'http://www.w3.org/1999/xhtml'
        }
    
    def parse_document(self, xml_content: str, document_metadata: Dict[str, Any]) -> Tuple[CommonAct, List[CommonArticle]]:
        """
        Parse a Vlaamse Codex XML document.
        
        Args:
            xml_content: Raw XML content
            document_metadata: Metadata from the API response
            
        Returns:
            Tuple of (CommonAct, List[CommonArticle])
            
        Raises:
            ParseError: If XML parsing fails
            ValueError: If required fields are missing
        """
        try:
            # Parse XML with lxml
            root = etree.fromstring(xml_content.encode('utf-8'))
        except etree.XMLSyntaxError as e:
            logger.error(f"XML parsing failed: {e}")
            raise ParseError(f"Invalid XML: {e}")
        
        # Extract act information
        act = self._extract_act(root, document_metadata)
        
        # Extract articles
        articles = self._extract_articles(root, act.id, act.language)
        
        logger.info(f"Parsed act {act.id} with {len(articles)} articles")
        return act, articles
    
    def _extract_act(self, root: _Element, metadata: Dict[str, Any]) -> CommonAct:
        """Extract CommonAct from XML root and metadata."""
        
        # Try to find title in various locations
        title = self._find_title(root)
        if not title:
            title = metadata.get('Titel', metadata.get('Title', 'Unknown Title'))
        
        # Extract date
        pub_date = self._extract_date(metadata)
        
        # Extract language
        language = self._extract_language(root, metadata)
        
        # Create unique ID from metadata
        act_id = self._create_act_id(metadata)
        
        # Extract ELI if available
        eli = metadata.get('ELI') or self._extract_eli(root)
        
        # Prepare metadata
        act_metadata = {
            'numac': metadata.get('NUMAC'),
            'type': metadata.get('Type'),
            'publication_date': pub_date.isoformat() if pub_date else None,
            'source_url': metadata.get('DocumentUrlXML'),
            'original_metadata': metadata
        }
        
        return CommonAct(
            id=act_id,
            title=title,
            date=pub_date or date.today(),
            language=language,
            source="vlaamse",
            eli=eli,
            metadata=act_metadata
        )
    
    def _extract_articles(self, root: _Element, act_id: str, language: str) -> List[CommonArticle]:
        """Extract articles from the XML document."""
        articles = []
        
        # Try different XPath patterns for articles
        article_patterns = [
            ".//akn:article",
            ".//article",
            ".//div[@class='article']",
            ".//div[contains(@class, 'artikel')]"
        ]
        
        article_elements = []
        for pattern in article_patterns:
            elements = root.xpath(pattern, namespaces=self.namespaces)
            if elements:
                article_elements = elements
                break
        
        if not article_elements:
            # Fallback: look for numbered sections or paragraphs
            article_elements = self._find_numbered_sections(root)
        
        for i, element in enumerate(article_elements):
            article = self._parse_article_element(element, act_id, language, i + 1)
            if article:
                articles.append(article)
        
        return articles
    
    def _parse_article_element(self, element: _Element, act_id: str, language: str, fallback_number: int) -> Optional[CommonArticle]:
        """Parse a single article element."""
        
        # Extract article number
        number = self._extract_article_number(element, fallback_number)
        
        # Extract heading
        heading = self._extract_article_heading(element)
        
        # Extract text content
        text = self._extract_article_text(element)
        
        if not text or len(text.strip()) < 10:
            logger.debug(f"Skipping article {number} - insufficient content")
            return None
        
        # Create article ID
        article_id = f"{act_id}#art{number}"
        
        return CommonArticle(
            id=article_id,
            act_id=act_id,
            number=str(number),
            heading=heading,
            text=text,
            language=language,
            metadata={
                'element_tag': element.tag,
                'has_heading': bool(heading)
            }
        )
    
    def _find_title(self, root: _Element) -> Optional[str]:
        """Find document title in XML."""
        title_patterns = [
            ".//akn:docTitle",
            ".//akn:title",
            ".//title",
            ".//h1",
            ".//div[@class='title']"
        ]
        
        for pattern in title_patterns:
            elements = root.xpath(pattern, namespaces=self.namespaces)
            if elements:
                title = self._clean_text(elements[0])
                if title:
                    return title
        
        return None
    
    def _extract_date(self, metadata: Dict[str, Any]) -> Optional[date]:
        """Extract publication date from metadata."""
        date_fields = ['DatumPublicatie', 'PublicationDate', 'Date']
        
        for field in date_fields:
            date_str = metadata.get(field)
            if date_str:
                try:
                    # Handle various date formats
                    if isinstance(date_str, str):
                        # Try ISO format first
                        if 'T' in date_str:
                            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                            return dt.date()
                        else:
                            return datetime.strptime(date_str, '%Y-%m-%d').date()
                    elif isinstance(date_str, datetime):
                        return date_str.date()
                    elif isinstance(date_str, date):
                        return date_str
                except (ValueError, TypeError) as e:
                    logger.warning(f"Failed to parse date {date_str}: {e}")
                    continue
        
        return None
    
    def _extract_language(self, root: _Element, metadata: Dict[str, Any]) -> str:
        """Extract document language."""
        # Check metadata first
        lang = metadata.get('Taal', metadata.get('Language'))
        if lang:
            # Map common language codes
            lang_map = {
                'Nederlands': 'nl',
                'Frans': 'fr', 
                'Duits': 'de',
                'Dutch': 'nl',
                'French': 'fr',
                'German': 'de'
            }
            return lang_map.get(lang, lang.lower()[:2])
        
        # Check XML lang attribute
        lang_attr = root.get('lang') or root.get('{http://www.w3.org/XML/1998/namespace}lang')
        if lang_attr:
            return lang_attr.lower()[:2]
        
        # Default to Dutch for Vlaamse documents
        return 'nl'
    
    def _create_act_id(self, metadata: Dict[str, Any]) -> str:
        """Create a unique act ID from metadata."""
        # Prefer NUMAC if available
        numac = metadata.get('NUMAC')
        if numac:
            return f"vlaamse_{numac}"
        
        # Fallback to other identifiers
        doc_id = metadata.get('Id') or metadata.get('DocumentId')
        if doc_id:
            return f"vlaamse_{doc_id}"
        
        # Last resort: create from title and date
        title = metadata.get('Titel', metadata.get('Title', 'unknown'))
        date_str = metadata.get('DatumPublicatie', 'unknown')
        safe_title = re.sub(r'[^\w\s-]', '', title)[:50]
        safe_title = re.sub(r'\s+', '_', safe_title.strip())
        return f"vlaamse_{safe_title}_{date_str}"
    
    def _extract_eli(self, root: _Element) -> Optional[str]:
        """Extract ELI from XML if present."""
        eli_patterns = [
            ".//akn:FRBRuri/@value",
            ".//akn:identification/akn:FRBRWork/akn:FRBRuri/@value",
            ".//@eli"
        ]
        
        for pattern in eli_patterns:
            results = root.xpath(pattern, namespaces=self.namespaces)
            if results:
                eli = results[0]
                if eli.startswith('eli:'):
                    return eli
        
        return None
    
    def _find_numbered_sections(self, root: _Element) -> List[_Element]:
        """Find numbered sections as fallback for articles."""
        # Look for elements with article-like patterns
        patterns = [
            ".//div[starts-with(normalize-space(.), 'Art.')]",
            ".//div[starts-with(normalize-space(.), 'Artikel')]",
            ".//p[starts-with(normalize-space(.), 'Art.')]",
            ".//p[starts-with(normalize-space(.), 'Artikel')]"
        ]
        
        for pattern in patterns:
            elements = root.xpath(pattern, namespaces=self.namespaces)
            if elements:
                return elements
        
        return []
    
    def _extract_article_number(self, element: _Element, fallback: int) -> str:
        """Extract article number from element."""
        # Look for number in various attributes
        num_attrs = ['num', 'number', 'id']
        for attr in num_attrs:
            value = element.get(attr)
            if value:
                # Extract numeric part
                match = re.search(r'\d+', value)
                if match:
                    return match.group()
        
        # Look for number in text content
        text = self._clean_text(element)
        if text:
            # Match patterns like "Art. 5", "Artikel 10", etc.
            patterns = [
                r'Art\.?\s*(\d+)',
                r'Artikel\s*(\d+)',
                r'^(\d+)\.',
                r'§\s*(\d+)'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    return match.group(1)
        
        return str(fallback)
    
    def _extract_article_heading(self, element: _Element) -> Optional[str]:
        """Extract article heading/title."""
        # Look for heading elements within the article
        heading_patterns = [
            ".//akn:heading",
            ".//h1", ".//h2", ".//h3", ".//h4",
            ".//div[@class='heading']",
            ".//span[@class='title']"
        ]
        
        for pattern in heading_patterns:
            headings = element.xpath(pattern, namespaces=self.namespaces)
            if headings:
                heading = self._clean_text(headings[0])
                if heading:
                    return heading
        
        return None
    
    def _extract_article_text(self, element: _Element) -> str:
        """Extract clean text content from article element."""
        # Get all text content
        text = self._clean_text(element)
        
        # Remove article number prefix if present
        text = re.sub(r'^(Art\.?\s*\d+\.?\s*[-–]?\s*)', '', text, flags=re.IGNORECASE)
        text = re.sub(r'^(Artikel\s*\d+\.?\s*[-–]?\s*)', '', text, flags=re.IGNORECASE)
        
        return text.strip()
    
    def _clean_text(self, element: _Element) -> str:
        """Extract and clean text content from an element."""
        if element is None:
            return ""
        
        # Get all text content including from child elements
        text_parts = element.xpath(".//text()")
        text = " ".join(part.strip() for part in text_parts if part.strip())
        
        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        return text
