"""
Vlaamse Codex API client for ailex-be-ingest.

<PERSON>les pagination through the Vlaamse Codex Open-Data API to retrieve
legal documents with proper rate limiting and error handling.
"""

import time
import logging
from datetime import datetime, date
from typing import Iterator, Dict, Any, Optional, List
from urllib.parse import urljoin

import requests
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from common.config import config

logger = logging.getLogger(__name__)


class VlaamseCodexClient:
    """Client for the Vlaamse Codex Open-Data API."""
    
    def __init__(self, base_url: Optional[str] = None, rate_limit: Optional[float] = None):
        """
        Initialize the Vlaamse Codex client.
        
        Args:
            base_url: Base URL for the API (defaults to config)
            rate_limit: Rate limit in requests per second (defaults to config)
        """
        self.base_url = base_url or config.VLAAMSE_CODEX_BASE_URL
        self.rate_limit = rate_limit or config.VLAAMSE_CODEX_RATE_LIMIT
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "ailex-be-ingest/1.0",
            "Accept": "application/json"
        })
        self.last_request_time = 0.0
    
    def _rate_limit_wait(self):
        """Enforce rate limiting between requests."""
        if self.rate_limit > 0:
            time_since_last = time.time() - self.last_request_time
            min_interval = 1.0 / self.rate_limit
            if time_since_last < min_interval:
                sleep_time = min_interval - time_since_last
                time.sleep(sleep_time)
        self.last_request_time = time.time()
    
    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=2, max=60),
        retry=retry_if_exception_type((requests.exceptions.RequestException, requests.exceptions.Timeout))
    )
    def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a rate-limited request to the API.
        
        Args:
            endpoint: API endpoint path
            params: Query parameters
            
        Returns:
            JSON response as dictionary
            
        Raises:
            requests.exceptions.RequestException: On API errors
        """
        self._rate_limit_wait()
        
        url = urljoin(self.base_url, endpoint)
        
        try:
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            if response.status_code == 429:
                logger.warning("Rate limit hit, retrying...")
                raise
            elif response.status_code >= 500:
                logger.warning(f"Server error {response.status_code}, retrying...")
                raise
            else:
                logger.error(f"HTTP error {response.status_code}: {e}")
                raise
        except requests.exceptions.RequestException as e:
            logger.warning(f"Request failed: {e}")
            raise
    
    def get_documents(
        self, 
        cursor_date: Optional[date] = None,
        page_size: int = 100,
        max_pages: Optional[int] = None
    ) -> Iterator[Dict[str, Any]]:
        """
        Paginate through documents from the Vlaamse Codex API.
        
        Args:
            cursor_date: Start date for filtering (DatumPublicatie ge cursor)
            page_size: Number of documents per page
            max_pages: Maximum number of pages to fetch (None for all)
            
        Yields:
            Document dictionaries from the API
        """
        endpoint = "/Document"
        skip = 0
        pages_fetched = 0
        
        # Build filter for date cursor
        filter_parts = []
        if cursor_date:
            filter_parts.append(f"DatumPublicatie ge {cursor_date.isoformat()}")
        
        base_params = {
            "$top": page_size,
            "$orderby": "DatumPublicatie asc",
            "$format": "json"
        }
        
        if filter_parts:
            base_params["$filter"] = " and ".join(filter_parts)
        
        logger.info(f"Starting document pagination from {cursor_date or 'beginning'}")
        
        while True:
            if max_pages and pages_fetched >= max_pages:
                logger.info(f"Reached maximum pages limit: {max_pages}")
                break
            
            params = {**base_params, "$skip": skip}
            
            try:
                response_data = self._make_request(endpoint, params)
                
                # Handle OData response format
                if "value" in response_data:
                    documents = response_data["value"]
                else:
                    documents = response_data if isinstance(response_data, list) else []
                
                if not documents:
                    logger.info("No more documents found, pagination complete")
                    break
                
                logger.info(f"Fetched page {pages_fetched + 1}: {len(documents)} documents")
                
                for doc in documents:
                    yield doc
                
                # Check if we got fewer documents than requested (last page)
                if len(documents) < page_size:
                    logger.info("Reached last page (partial results)")
                    break
                
                skip += page_size
                pages_fetched += 1
                
            except Exception as e:
                logger.error(f"Error fetching page {pages_fetched + 1}: {e}")
                raise
    
    def get_document_by_id(self, document_id: str) -> Optional[Dict[str, Any]]:
        """
        Fetch a specific document by ID.
        
        Args:
            document_id: Document identifier
            
        Returns:
            Document dictionary or None if not found
        """
        endpoint = f"/Document('{document_id}')"
        params = {"$format": "json"}
        
        try:
            return self._make_request(endpoint, params)
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                logger.warning(f"Document {document_id} not found")
                return None
            raise
    
    def download_xml(self, xml_url: str) -> Optional[str]:
        """
        Download XML content from a document URL.
        
        Args:
            xml_url: URL to the XML document
            
        Returns:
            XML content as string or None if failed
        """
        self._rate_limit_wait()
        
        try:
            response = self.session.get(xml_url, timeout=60)
            response.raise_for_status()
            
            # Ensure we got XML content
            content_type = response.headers.get("content-type", "")
            if "xml" not in content_type.lower():
                logger.warning(f"Expected XML but got {content_type}")
            
            return response.text
        except Exception as e:
            logger.error(f"Failed to download XML from {xml_url}: {e}")
            return None
    
    def get_document_stats(self, since_date: Optional[date] = None) -> Dict[str, int]:
        """
        Get statistics about documents in the API.
        
        Args:
            since_date: Count documents since this date
            
        Returns:
            Dictionary with count statistics
        """
        endpoint = "/Document/$count"
        params = {}
        
        if since_date:
            params["$filter"] = f"DatumPublicatie ge {since_date.isoformat()}"
        
        try:
            # The $count endpoint returns a plain number, not JSON
            response = self.session.get(
                urljoin(self.base_url, endpoint), 
                params=params, 
                timeout=30
            )
            response.raise_for_status()
            
            total_count = int(response.text.strip())
            return {"total_documents": total_count}
        except Exception as e:
            logger.error(f"Failed to get document stats: {e}")
            return {"total_documents": 0}
    
    def close(self):
        """Close the HTTP session."""
        self.session.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
