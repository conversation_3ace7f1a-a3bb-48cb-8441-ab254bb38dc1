# AiLex-BE-Ingest 🏛️⚖️

> **Intelligent Legal Data Infrastructure for Belgian & EU Law**
> *Powering the next generation of legal AI through comprehensive data ingestion, semantic search, and graph-based legal reasoning*

---

## 🎯 **Vision & Mission**

**AiLex-BE-Ingest** is the foundational data infrastructure that transforms static legal documents into an intelligent, searchable, and interconnected knowledge graph. This repository serves as the **data backbone** for the Core AiLex application, providing comprehensive coverage of Belgian and EU legal systems through advanced AI-powered ingestion and retrieval capabilities.

### **What We're Building**

A **world-class legal data platform** that:

- 📚 **Ingests 30+ years** of Belgian legal documents (Moniteur Belge, case law, EU directives)
- 🧠 **Understands legal semantics** using state-of-the-art embeddings (Voyage AI)
- 🔍 **Enables intelligent search** across millions of legal provisions and precedents
- 🕸️ **Maps legal relationships** in a comprehensive knowledge graph (Neo4j)
- ⚡ **Serves real-time queries** to AI agents and legal professionals
- 🔄 **Maintains freshness** with automated daily updates from official sources

### **The Complete Legal AI Ecosystem**

```mermaid
graph TB
    subgraph "Data Sources"
        A[📋 Moniteur Belge<br/>30 years CC-0]
        B[⚖️ Belgian Case Law<br/>ECLI API]
        C[🇪🇺 EU Law<br/>EUR-Lex/CELEX]
        D[🏛️ ECHR Decisions<br/>HUDOC]
    end

    subgraph "AiLex-BE-Ingest (This Repo)"
        E[🔄 ETL Pipelines]
        F[🧠 Voyage AI Embeddings<br/>1024-dim vectors]
        G[📊 Pinecone Vector DB<br/>Semantic Search]
        H[🕸️ Neo4j Knowledge Graph<br/>Legal Relationships]
        I[🔍 Retrieval API<br/>Search + Rerank + GraphRAG]
    end

    subgraph "Core AiLex App"
        J[🤖 AI Legal Agents]
        K[💬 User Interface]
        L[📈 Analytics & Insights]
    end

    A --> E
    B --> E
    C --> E
    D --> E
    E --> F
    F --> G
    E --> H
    G --> I
    H --> I
    I --> J
    J --> K
    J --> L
```

---

## 🚀 **Key Capabilities**

### **📥 Comprehensive Data Ingestion**
- **Belgian State Gazette**: 784k+ documents (1995-2025) via HF dataset + daily RSS updates
- **Case Law**: Belgian court decisions via OpenJustice ECLI API
- **EU Legislation**: Directives, regulations, and decisions via EUR-Lex SPARQL
- **Human Rights**: ECHR decisions affecting Belgium via HUDOC API

### **🧠 Advanced AI Processing**
- **Semantic Embeddings**: Voyage-3-large (1024 dimensions) for deep legal understanding
- **Intelligent Chunking**: 1000-token chunks with 100-token overlap for optimal context
- **Cross-Language Support**: Dutch, French, German (Belgium's official languages)
- **Legal Entity Recognition**: Automatic extraction of ELI, ECLI, CELEX identifiers

### **🔍 Intelligent Retrieval System**
- **Vector Similarity Search**: Sub-100ms semantic search across millions of documents
- **Advanced Reranking**: Voyage rerank-2 for precision-optimized results
- **GraphRAG**: Knowledge graph expansion for comprehensive legal context
- **Multi-Modal Queries**: Support for natural language, legal citations, and structured queries

### **🕸️ Legal Knowledge Graph**
- **Relationship Mapping**: Amendments, repeals, implementations, citations
- **Temporal Versioning**: Track legal changes over time
- **Cross-Jurisdictional Links**: Connect Belgian law to EU directives and ECHR precedents
- **Citation Networks**: Discover legal precedents and influential decisions

---

## 🎯 **Use Cases & Applications**

### **For Legal Professionals**
- 📖 **Research**: "Find all Belgian laws implementing EU GDPR requirements"
- 📅 **Historical Analysis**: "How have privacy laws evolved since 2018?"
- ⚖️ **Case Law**: "Show precedents for Article 8 ECHR violations in Belgium"
- 🔍 **Due Diligence**: "What regulatory changes affect fintech companies?"

### **For AI Agents**
- 🤖 **Legal Q&A**: Provide accurate answers with authoritative citations
- 📋 **Compliance Monitoring**: Alert on relevant regulatory changes
- 📊 **Document Analysis**: Extract key provisions and obligations
- 🔗 **Relationship Discovery**: Map connections between legal instruments

### **For Businesses**
- ✅ **Regulatory Compliance**: Stay current with applicable laws
- 🏢 **Corporate Formation**: Get accurate requirements for business setup
- 📈 **Risk Assessment**: Identify legal risks in business operations
- 🌍 **Cross-Border**: Navigate Belgian-EU legal interactions

---

## 🏗️ **Technical Architecture**

### **Core Components**
- **🔄 ETL Workers**: Python-based ingestion pipelines with robust error handling
- **📊 Vector Store**: Pinecone (cosine similarity, 1024-dim) for semantic search
- **🕸️ Graph Database**: Neo4j for legal relationship modeling
- **🔍 Retrieval API**: FastAPI service exposing search, rerank, and graph capabilities
- **📈 Monitoring**: Comprehensive observability with Prometheus/Grafana

### **Data Model**
```python
# Pinecone Namespaces
moniteur: {eli, numac, lang, type, pub_date, title, source_url}
ecli: {ecli, court, chamber, date, citations, title}
eu: {celex, type, date, title, directory_codes}
echr: {hudoc_id, date, title, articles, country_codes}

# Neo4j Graph Schema
(:ACT)-[:HAS_ARTICLE]->(:ARTICLE)
(:ARTICLE)-[:HAS_VERSION]->(:VERSION)
(:VERSION)-[:AMENDS]->(:VERSION)
(:JUDGMENT)-[:CITES]->(:ARTICLE)
(:ARTICLE)-[:IMPLEMENTS]->(:EU_INSTRUMENT)
```

### **Performance Targets**
- **🚀 Search Latency**: <100ms (p95) for vector similarity search
- **🔄 Rerank Speed**: <300ms (p95) for cross-encoder reranking
- **📊 Graph Queries**: <200ms (p95) for relationship expansion
- **📅 Data Freshness**: <24h lag for new legal publications
- **⚡ Availability**: 99.5% uptime with automatic failover

---

## 📊 **Impact & Scale**

### **Data Coverage**
- **📚 Documents**: 784k+ Belgian legal publications (1995-2025)
- **⚖️ Case Law**: Comprehensive Belgian court decisions via ECLI
- **🇪🇺 EU Integration**: Key directives and regulations affecting Belgium
- **🏛️ Human Rights**: ECHR decisions with Belgian relevance
- **🔄 Daily Updates**: Automated ingestion of new publications

### **Search Quality**
- **🎯 Precision**: High-relevance results through semantic understanding
- **📈 Recall**: Comprehensive coverage across 30 years of legal documents
- **🌐 Multi-Language**: Seamless search across Dutch, French, German
- **🔗 Context**: Rich metadata and relationship information

### **Business Value**
- **⏱️ Time Savings**: Reduce legal research from hours to minutes
- **✅ Accuracy**: Authoritative sources with proper legal citations
- **📊 Insights**: Discover patterns and trends in legal evolution
- **🤖 AI-Ready**: Purpose-built for integration with legal AI agents

---

## 🛣️ **Development Roadmap**

### **Phase 1: Foundation (Weeks 1-2)** ✅
- [x] Moniteur Belge backfill via HF dataset
- [x] Voyage AI embedding integration
- [x] Pinecone vector storage setup
- [x] Basic retrieval API

### **Phase 2: Real-Time Updates (Weeks 3-5)**
- [ ] ETAAMB RSS feed monitoring
- [ ] Belgian case law ingestion (ECLI API)
- [ ] Advanced reranking service
- [ ] Comprehensive error handling

### **Phase 3: Knowledge Graph (Weeks 6-8)**
- [ ] Neo4j graph database setup
- [ ] Legal relationship extraction
- [ ] GraphRAG implementation
- [ ] EU law integration (EUR-Lex)

### **Phase 4: Production Ready (Weeks 9-10)**
- [ ] ECHR integration (HUDOC)
- [ ] Evaluation framework
- [ ] Performance optimization
- [ ] Production deployment

---

## 🤝 **Integration with Core AiLex**

This repository provides the **data infrastructure layer** that powers the Core AiLex application. The integration flow:

1. **📥 Data Ingestion**: Automated pipelines populate the knowledge base
2. **🔍 Query Processing**: Core AiLex sends search requests to our API
3. **🧠 Context Retrieval**: We return relevant legal documents and relationships
4. **🤖 AI Generation**: Core AiLex uses our context for accurate legal responses
5. **📊 Feedback Loop**: Query patterns inform our optimization efforts

**API Endpoints for Core AiLex:**
- `POST /search` - Semantic search across legal documents
- `POST /rerank` - Precision reranking of search results
- `GET /graph/expand` - Knowledge graph relationship expansion
- `GET /documents/{id}` - Full document retrieval with metadata

---

---

## 🏗️ **Implementation Status & Architecture**

### **Current Implementation (Phase A Complete)**

✅ **Unified Data Models**
- `CommonAct` and `CommonArticle` models for source-agnostic processing
- Neo4j schema with proper indexes and constraints
- Pydantic validation and JSON serialization

✅ **Vlaamse Codex Integration**
- API client with rate limiting and pagination
- XML parser using lxml with robust error handling
- Complete ingestion pipeline with atomic transactions and checkpointing

✅ **Infrastructure**
- Neo4j knowledge graph with relationship modeling
- Pinecone vector storage with Voyage AI embeddings
- Google Cloud Storage for raw file archival
- Prefect orchestration with error handling and retries
- Robust checkpointing and idempotent operations

✅ **Testing & Quality**
- Comprehensive unit tests with pytest
- Mock fixtures for external services
- CI/CD ready with GitHub Actions support

### **Architecture Overview**

```mermaid
graph TB
    subgraph "Phase A - Vlaamse Codex (✅ COMPLETE)"
        A1[Vlaamse Codex API] --> A2[XML Parser]
        A2 --> A3[CommonAct/Article Models]
    end

    subgraph "Phase B - EUR-Lex (🚧 PLANNED)"
        B1[EUR-Lex Bulk Download] --> B2[FORMEX Parser]
        B2 --> B3[CommonAct/Article Models]
    end

    subgraph "Phase C - CELLAR (🚧 PLANNED)"
        C1[CELLAR SPARQL] --> C2[REST API]
        C2 --> C3[CommonAct/Article Models]
    end

    subgraph "Unified Processing Pipeline"
        A3 --> D1[Neo4j Knowledge Graph]
        B3 --> D1
        C3 --> D1

        A3 --> D2[Text Chunking]
        B3 --> D2
        C3 --> D2

        D2 --> D3[Voyage AI Embeddings]
        D3 --> D4[Pinecone Vector Store]

        A3 --> D5[Raw File Storage]
        B3 --> D5
        C3 --> D5
        D5 --> D6[Google Cloud Storage]
    end

    subgraph "Orchestration & Monitoring"
        E1[Prefect Workflows] --> A1
        E1 --> B1
        E1 --> C1
        E2[Logging & Metrics] --> E1
    end
```

### **Data Flow**

1. **Source APIs** → Raw JSON/XML documents
2. **Parsers** → Structured `CommonAct`/`CommonArticle` models
3. **Neo4j** → Knowledge graph with legal relationships
4. **Chunking** → 1000-token overlapping text segments
5. **Voyage AI** → 1024-dimensional semantic embeddings
6. **Pinecone** → Vector storage with metadata for retrieval
7. **GCS** → Raw file archival for audit and reprocessing

*Building the future of legal AI, one document at a time.* 🚀⚖️

---

## 🚀 **Quick Start Guide**

### **Prerequisites**

1. **System Requirements**:
   - Python 3.9+
   - Neo4j 5.0+ (for knowledge graph)
   - Google Cloud account (for raw file storage)
   - Pinecone account (for vector storage)
   - Voyage AI API key (for embeddings)

2. **Environment Setup**:
   ```bash
   # Clone the repository
   git clone https://github.com/Jpkay/ailex-be-ingest.git
   cd ailex-be-ingest

   # Install dependencies
   pip install -r requirements.txt
   ```

3. **Environment Variables** (create `.env` file):
   ```bash
   # Required - AI/ML Services
   VOYAGE_API_KEY=your_voyage_api_key
   PINECONE_API_KEY=your_pinecone_api_key
   PINECONE_INDEX=ailexbe

   # Required - Neo4j Knowledge Graph
   NEO4J_URI=bolt://localhost:7687
   NEO4J_USERNAME=neo4j
   NEO4J_PASSWORD=your_neo4j_password

   # Required - Global Registry (Supabase)
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_KEY=your_supabase_anon_key

   # Optional - Google Cloud Storage (for raw files)
   GCS_BUCKET=ailex-be
   GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

   # Optional - Hugging Face (for existing Moniteur pipeline)
   HF_TOKEN=your_huggingface_token

   # Optional - Processing Configuration
   CHUNK_SIZE=1000
   CHUNK_OVERLAP=100
   BATCH_EMBED=64
   LOG_LEVEL=INFO

   # Phase B & C - EUR-Lex Configuration (for future phases)
   EURLEX_USERNAME=your_eu_login_username
   EURLEX_PASSWORD=your_eu_login_password
   ```

### **Initial Setup**

1. **Set up Supabase Database**:
   ```bash
   # Create a new Supabase project at https://supabase.com
   # Copy your project URL and anon key to .env file

   # Run the migration to create registry table
   # (Use Supabase dashboard SQL editor or CLI)
   cat supabase/migrations/20250126_create_registry.sql
   ```

2. **Set up Neo4j Database**:
   ```bash
   # Start Neo4j (using Docker)
   docker run -d \
     --name neo4j \
     -p 7474:7474 -p 7687:7687 \
     -e NEO4J_AUTH=neo4j/your_password \
     neo4j:5.0

   # Or install locally and start
   # Follow: https://neo4j.com/docs/operations-manual/current/installation/
   ```

2. **Initialize Database Schema**:
   ```bash
   python scripts/setup_neo4j.py
   ```

3. **Test the Pipeline** (dry run):
   ```bash
   python pipelines/initial_load.py --dry-run --limit 5 --vlaamse-only
   ```

### **Running the Complete Pipeline**

1. **Phase A - Vlaamse Codex** (Priority 1):
   ```bash
   # Process last 30 days of documents (with automatic resume)
   python pipelines/initial_load.py --since-days 30 --vlaamse-only

   # Process specific number of documents
   python pipelines/initial_load.py --limit 50 --vlaamse-only

   # Reset checkpoint and start fresh
   python pipelines/initial_load.py --reset --limit 50 --vlaamse-only

   # Check checkpoint status
   python sources/vlaamse_codex/ingest.py --status

   # Full historical load (careful - this is large!)
   python pipelines/initial_load.py --vlaamse-only
   ```

2. **Checkpoint Management**:
   ```bash
   # Resume from checkpoint (default behavior)
   python pipelines/initial_load.py --resume --vlaamse-only

   # Start fresh without checkpoint
   python pipelines/initial_load.py --no-resume --vlaamse-only

   # Reset checkpoint and start over
   python pipelines/initial_load.py --reset --vlaamse-only
   ```

3. **All Phases** (when Phase B & C are implemented):
   ```bash
   # Run complete pipeline
   python pipelines/initial_load.py --limit 50
   ```

### **Monitoring and Verification**

1. **Check Neo4j Data**:
   ```cypher
   // Open Neo4j Browser at http://localhost:7474
   MATCH (a:Act) RETURN a.source, count(a) as acts
   MATCH (art:Article) RETURN art.language, count(art) as articles
   ```

2. **Check Pinecone Vectors**:
   ```python
   from pinecone import Pinecone
   pc = Pinecone(api_key="your_key")
   index = pc.Index("ailexbe")
   stats = index.describe_index_stats()
   print(f"Total vectors: {stats.total_vector_count}")
   ```

3. **Check Global Registry**:
   ```bash
   # View registry stats in Supabase dashboard or via SQL:
   SELECT consistency_status, COUNT(*) FROM registry_consistency_view GROUP BY consistency_status;
   ```

4. **Run Tests**:
   ```bash
   pytest tests/ -v
   ```

### **Checkpoint & Resume Features**

**Robust Ingestion:**
- ✅ **Atomic Operations**: Each document is processed completely or not at all
- ✅ **Persistent Checkpoints**: Resume from exact point after interruption
- ✅ **Idempotent Upserts**: No duplicates even if reprocessed
- ✅ **Deterministic Vector IDs**: Same content always gets same vector ID

**Checkpoint Storage:**
- 🏠 **Local Files**: `data/checkpoints/{source}.json` (default)
- ☁️ **Google Cloud Storage**: `gs://bucket/checkpoints/{source}.json` (if configured)

**Interruption Handling:**
```bash
# Start ingestion
python pipelines/initial_load.py --limit 100 --vlaamse-only

# Press Ctrl+C to interrupt...
# Progress is automatically saved

# Resume from where you left off
python pipelines/initial_load.py --vlaamse-only
# Will continue from last processed document
```

### **Audit & Repair Tools**

**Consistency Auditing:**
```bash
# Audit all documents for consistency
python tools/audit.py --source vlaamse --since 2024-01-01

# Generate detailed CSV report
python tools/audit.py --output audit_report.csv --fail-on-errors

# Check specific time period
python tools/audit.py --since 2024-01-01 --limit 100
```

**Repair Inconsistencies:**
```bash
# Repair missing Neo4j nodes
python tools/repair.py --missing-neo4j --source vlaamse

# Repair vector mismatches
python tools/repair.py --vec-mismatch --limit 50

# Repair all inconsistencies (dry run first)
python tools/repair.py --all --dry-run
python tools/repair.py --all --limit 10
```

**Registry Management:**
- 📊 **Global Registry**: Tracks every document across all 4 stores
- 🔍 **Consistency Auditor**: Verifies data integrity across stores
- 🔧 **Repair Tools**: Fixes inconsistencies by reloading from GCS
- 📈 **Monitoring**: Built-in stats and reporting

### **Troubleshooting**

**Common Issues:**

1. **Neo4j Connection Failed**
   ```bash
   # Check if Neo4j is running
   docker ps | grep neo4j

   # Check connection
   python -c "from common.neo4j import Neo4jClient; Neo4jClient().close()"
   ```

2. **Voyage AI Rate Limits**
   ```bash
   # Check API key
   curl -H "Authorization: Bearer $VOYAGE_API_KEY" \
        https://api.voyageai.com/v1/models
   ```

3. **Pinecone Index Not Found**
   ```python
   from pinecone import Pinecone
   pc = Pinecone(api_key="your_key")
   print([idx.name for idx in pc.list_indexes()])
   ```

4. **Memory Issues with Large Datasets**
   ```bash
   # Use smaller batch sizes
   export BATCH_EMBED=32
   export CHUNK_SIZE=500
   ```

### **Next Steps & Roadmap**

**Phase B - EUR-Lex Implementation** (Weeks 3-4):
- [ ] EU-Login authentication handling
- [ ] FORMEX XML schema parser
- [ ] Bulk download stream processing
- [ ] Integration with unified pipeline

**Phase C - CELLAR Integration** (Weeks 5-6):
- [ ] SPARQL query client
- [ ] REST API integration
- [ ] Delta update mechanism
- [ ] Relationship extraction

**Production Readiness** (Weeks 7-8):
- [ ] Performance optimization
- [ ] Monitoring and alerting
- [ ] Backup and recovery procedures
- [ ] Load testing and scaling

**Advanced Features** (Future):
- [ ] Legal relationship extraction (AMENDS, REPEALS, CITES)
- [ ] Multi-language support optimization
- [ ] Real-time update streaming
- [ ] Advanced search and retrieval API

### **Running the Ingestion Worker**

#### **Test Run** (recommended first):
```bash
# Process 200 documents to verify setup
python -m etl.moniteur.hf_stream --dataset guust-franssens/belgian-journal --limit 200
```

#### **Production Run**:
```bash
# Full historical backfill with resume capability
python -m etl.moniteur.hf_stream --dataset guust-franssens/belgian-journal --resume
```

#### **Background Processing** (Mac mini):
```bash
# Option 1: Using tmux (recommended)
tmux new-session -d -s ailex-ingest
tmux send-keys -t ailex-ingest "cd /path/to/ailex-be-ingest" Enter
tmux send-keys -t ailex-ingest "python -m etl.moniteur.hf_stream --resume" Enter

# Option 2: Using nohup
nohup python -m etl.moniteur.hf_stream --resume > logs/ingest.out 2>&1 &

# Check progress
tail -f logs/hf_stream.log
```

### **Mac mini Long-Run Setup**

To ensure uninterrupted processing on Mac mini:

1. **Prevent Sleep**:
   ```bash
   # System Preferences > Energy Saver > Prevent computer from sleeping automatically when display is off
   sudo pmset -c sleep 0
   sudo pmset -c displaysleep 10
   ```

2. **Monitor Process**:
   ```bash
   # Check if process is running
   ps aux | grep hf_stream

   # Monitor logs
   tail -f logs/hf_stream.log

   # Check tmux session
   tmux list-sessions
   tmux attach-session -t ailex-ingest
   ```

3. **Auto-restart on Reboot** (optional):
   ```bash
   # Add to crontab
   @reboot cd /path/to/ailex-be-ingest && python -m etl.moniteur.hf_stream --resume
   ```

### **CLI Options**

```bash
python -m etl.moniteur.hf_stream [OPTIONS]

Options:
  --dataset TEXT          HuggingFace dataset name [default: guust-franssens/belgian-journal]
  --namespace TEXT        Pinecone namespace [default: moniteur]
  --resume / --no-resume  Resume from checkpoint [default: resume]
  --limit INTEGER         Limit documents for testing
  --batch-embed INTEGER   Embedding batch size [default: 64]
  --checkpoint-path TEXT  Checkpoint file path
  --help                  Show this message and exit
```

### **Monitoring & Troubleshooting**

#### **Check Processing Status**:
```bash
# View recent logs
tail -n 100 logs/hf_stream.log

# Check checkpoint
cat data/checkpoints/moniteur.progress

# Verify Pinecone vectors
python -c "
from pinecone import Pinecone
import os
from dotenv import load_dotenv
load_dotenv()
pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
idx = pc.Index(os.getenv('PINECONE_INDEX', 'ailex-be-legislation'))
stats = idx.describe_index_stats()
print(f'Total vectors: {stats.total_vector_count}')
print(f'Moniteur namespace: {stats.namespaces.get(\"moniteur\", {}).vector_count}')
"
```

#### **Common Issues**:

- **Rate Limits (429)**: Worker automatically handles with exponential backoff
- **Network Issues**: Built-in retry logic with tenacity
- **Memory Issues**: Reduce `--batch-embed` size (try 32 or 16)
- **Disk Space**: Monitor `logs/` and `data/` directories

#### **Performance Tuning**:
```bash
# For faster processing (if no rate limits)
python -m etl.moniteur.hf_stream --batch-embed 128

# For memory-constrained environments
python -m etl.moniteur.hf_stream --batch-embed 16

# Resume from specific checkpoint
# Edit data/checkpoints/moniteur.progress manually if needed
```

---