#!/usr/bin/env python3
"""
Setup script for ailex-be-ingest.

Provides easy installation and development setup.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README for long description
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding="utf-8") if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_path.exists():
    requirements = requirements_path.read_text().strip().split('\n')
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]

setup(
    name="ailex-be-ingest",
    version="1.0.0",
    description="Intelligent Legal Data Infrastructure for Belgian & EU Law",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="AiLex Team",
    author_email="<EMAIL>",
    url="https://github.com/Jpkay/ailex-be-ingest",
    packages=find_packages(),
    python_requires=">=3.9",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "black>=22.0.0",
            "isort>=5.0.0",
            "flake8>=4.0.0",
            "mypy>=0.950",
        ],
        "test": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=3.0.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "ailex-ingest=pipelines.initial_load:main",
            "ailex-setup-neo4j=scripts.setup_neo4j:main",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Legal Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Text Processing :: Linguistic",
    ],
    keywords="legal ai nlp knowledge-graph vector-database belgium eu-law",
    project_urls={
        "Bug Reports": "https://github.com/Jpkay/ailex-be-ingest/issues",
        "Source": "https://github.com/Jpkay/ailex-be-ingest",
        "Documentation": "https://github.com/Jpkay/ailex-be-ingest#readme",
    },
)
